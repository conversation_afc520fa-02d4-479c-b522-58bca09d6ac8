<div
  id="app-container"
  [class.login-active]="true"
  [class.header-hidden]="!isHeaderVisible"
>
  <div class="panel">
    <div #dialogHost></div>
  </div>
  <div class="glass-effect"></div>

  <!-- Retractable Header - Now part of main flow -->
  <app-nav-header
    *ngIf="showHeaderAndNav"
    [class.visible]="isHeaderVisible"
    [class.hidden]="!isHeaderVisible"
    class="retractable-header"
  >
  </app-nav-header>

  <div class="content-wrapper" [class.post-login]="showHeaderAndNav">
    <app-loader></app-loader>
    <router-outlet></router-outlet>
  </div>
</div>
