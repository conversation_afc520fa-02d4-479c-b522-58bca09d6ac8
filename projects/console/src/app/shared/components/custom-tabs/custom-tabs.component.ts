import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { LucideAngularModule } from 'lucide-angular';

export interface CustomTab {
  label: string;
  value: string | number;
  icon?: string; // Lucide icon name
  disabled?: boolean;
  iconColor?: string;
}

@Component({
  selector: 'app-custom-tabs',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './custom-tabs.component.html',
  styleUrls: ['./custom-tabs.component.scss'],
})
export class CustomTabsComponent implements OnInit {
  @Input() tabs: CustomTab[] = [];
  @Input() activeTab: string | number = '';
  @Input() variant: 'default' | 'icon' = 'icon';
  @Output() tabChange = new EventEmitter<string | number>();

  constructor() {}

  ngOnInit(): void {
    // Set default active tab if none provided
    if (!this.activeTab && this.tabs.length > 0) {
      this.activeTab = this.tabs[0].value;
    }
  }

  onTabClick(tabValue: string | number): void {
    if (this.isTabDisabled(tabValue)) {
      return;
    }
    
    this.activeTab = tabValue;
    this.tabChange.emit(tabValue);
  }

  isActiveTab(tabValue: string | number): boolean {
    return this.activeTab === tabValue;
  }

  isTabDisabled(tabValue: string | number): boolean {
    const tab = this.tabs.find(t => t.value === tabValue);
    return tab?.disabled || false;
  }

  getTabIcon(tab: CustomTab): string {
    return tab.icon || 'Circle';
  }

  getTabIconColor(tab: CustomTab): string {
    return tab.iconColor || '#6B7280';
  }
}
