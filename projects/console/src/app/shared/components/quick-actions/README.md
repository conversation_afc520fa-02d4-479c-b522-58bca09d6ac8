# Quick Actions Component

A reusable, collapsible quick actions panel component for the console application.

## Features

- **Collapsible Design**: Starts in a collapsed state showing only icons, expands to show full labels
- **Customizable Actions**: Accept custom actions via input property
- **Event Handling**: Emits events when actions are clicked
- **Responsive**: Adapts to different screen sizes
- **Accessible**: Includes proper ARIA labels and keyboard navigation
- **Themed**: Supports light/dark themes

## Usage

### Basic Usage

```typescript
import { QuickActionsComponent, QuickAction } from '@shared/components';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [QuickActionsComponent],
  template: `
    <app-quick-actions 
      [actions]="quickActions"
      (actionClick)="handleActionClick($event)">
    </app-quick-actions>
  `
})
export class DashboardComponent {
  quickActions: QuickAction[] = [
    { id: 'build-agent', label: 'Build Agent', icon: '/svgs/icons/awe_add_modetor.svg' },
    { id: 'build-workflow', label: 'Build Workflow', icon: '/svgs/icons/awe_add_chart.svg' },
    { id: 'create-prompt', label: 'Create Prompt', icon: '/svgs/icons/awe_add_bookmark_filled.svg' }
  ];

  handleActionClick(action: QuickAction) {
    console.log('Action clicked:', action);
    // Handle the action
  }
}
```

### Custom Actions with Callbacks

```typescript
quickActions: QuickAction[] = [
  { 
    id: 'build-agent', 
    label: 'Build Agent', 
    icon: '/svgs/icons/awe_add_modetor.svg',
    action: () => this.navigateToAgentBuilder()
  },
  { 
    id: 'build-workflow', 
    label: 'Build Workflow', 
    icon: '/svgs/icons/awe_add_chart.svg',
    action: () => this.navigateToWorkflowBuilder()
  }
];
```

### Styling

The component uses CSS custom properties for theming:

```scss
app-quick-actions {
  --quick-actions-bg: #your-bg-color;
  --quick-actions-border: #your-border-color;
  --quick-actions-text: #your-text-color;
  --quick-actions-button-bg: #your-button-bg;
  --quick-actions-button-hover: #your-button-hover;
}
```

## API

### Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `actions` | `QuickAction[]` | Default actions | Array of quick action items |

### Outputs

| Event | Type | Description |
|-------|------|-------------|
| `actionClick` | `QuickAction` | Emitted when an action is clicked |

### QuickAction Interface

```typescript
interface QuickAction {
  id: string;           // Unique identifier
  label: string;        // Display label
  icon: string;         // Icon path/URL
  action?: () => void;  // Optional callback function
}
```

## Accessibility

- Uses semantic HTML elements
- Includes proper ARIA labels
- Supports keyboard navigation
- Provides tooltips for collapsed icons

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+ 