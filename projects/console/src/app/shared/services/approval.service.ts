import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ApprovalService {

  private baseUrl = environment.consoleApiV2;

  constructor(private http: HttpClient) {

  }

  public getAllReviewTools(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrl}/ava/force/da/userTools/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());

    return this.http.get<any>(url, { params });
  }

  public getAllReviewAgents(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrl}/ava/force/da/agent/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());

    return this.http.get<any>(url, { params });
  }

  public approveTool(id: number, toolId: number, status: string, reviewedBy: string) {
      const url = `${this.baseUrl}/ava/force/da/userTools/review`;
      const body = {
          id: id,
          toolId: toolId,
          status: status,
          reviewedBy: reviewedBy
      };

      return this.http.put(url, body);
  }

  public rejectTool(id: number, toolId: number, status: string, reviewedBy: string, message: string){
    // reject tool code
    const url = `${this.baseUrl}/ava/force/da/userTools/review`;
    const body = {
      id: id,
      toolId: toolId,
      status: status,
      comments: message,
      reviewedBy: reviewedBy
    };

    return this.http.put(url, body);
  }

  public approveAgent(id: number, agentId: number, status: string, reviewedBy: string) {
      const url = `${this.baseUrl}/ava/force/da/agent/review`;
      const body = {
          id: id,
          agentId: agentId,
          status: status,
          reviewedBy: reviewedBy
      };

      return this.http.put(url, body);
  }

  public rejectAgent(id: number, agentId: number, status: string, reviewedBy: string, message: string){
    // reject tool code
    const url = `${this.baseUrl}/ava/force/da/agent/review`;
    const body = {
      id: id,
      agentId: agentId,
      status: status,
      comments: message,
      reviewedBy: reviewedBy
    };

    return this.http.put(url, body);
  }
}