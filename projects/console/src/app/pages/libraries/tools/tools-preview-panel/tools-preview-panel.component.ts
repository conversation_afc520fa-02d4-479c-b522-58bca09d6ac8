import { Component, Input } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TOOLS_DATA } from '../constants/builtInTools';
import { ButtonComponent, IconComponent } from '@ava/play-comp-library';
import { CommonModule } from '@angular/common';
import { PreviewPanelComponent } from 'projects/console/src/app/shared/components/preview-panel/preview-panel.component';

@Component({
  selector: 'app-tools-preview-panel',
  imports: [
    ButtonComponent,
    IconComponent,
    CommonModule,
    PreviewPanelComponent,
  ],
  templateUrl: './tools-preview-panel.component.html',
  styleUrl: './tools-preview-panel.component.scss',
})
export class ToolsPreviewPanelComponent {
  @Input() selectedTool: any;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
  ) {}
  @Input() closePreview!: () => void;
  toolsData = TOOLS_DATA;

  onButtonClick(event: any): void {
    this.closePreview();
    this.router.navigate(['/build/agents/build-agents', 'collaborative']);
  }
}
