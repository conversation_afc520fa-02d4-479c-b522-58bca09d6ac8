<div class="workflow-editor-container">
  <!-- Header Navigation -->
  <!-- <div class="header-nav">
    <div class="breadcrumb">
      <span class="nav-item">Workflows</span>
      <span class="separator">/</span>
      <span class="nav-item active">Workflow Editor</span>
      <button class="close-btn" title="Close">
        <ava-icon iconName="X" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
      </button>
    </div>
    <div class="header-actions">
      <div class="action-group">
        <button class="action-btn" title="Undo">
          <ava-icon iconName="Undo" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
        </button>
        <button class="action-btn" title="Redo">
          <ava-icon iconName="Redo" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
        </button>
        <button class="action-btn" title="Refresh">
          <ava-icon iconName="RefreshCw" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
        </button>
        <button class="run-btn" title="Execute Workflow">
          <ava-icon iconName="Play" iconSize="16" iconColor="white"></ava-icon>
          Execute
        </button>
      </div>
    </div>
  </div> -->

  <!-- Main Content Area -->
  <div class="main-content">
    <!-- Full Width Canvas Area -->
    <div class="canvas-area">
      <!-- Agent Library Floating Panel -->
      <div class="agent-library-panel" [class.collapsed]="isPanelCollapsed">
        <div class="panel-header" (click)="togglePanel()">
          <h3>Agent Library</h3>
          <ava-icon
            [iconName]="isPanelCollapsed ? 'ChevronDown' : 'ChevronUp'"
            iconSize="16"
            iconColor="var(--text-secondary)"
          >
          </ava-icon>
        </div>

        <!-- Panel Content -->
        <div class="panel-content" [class.hidden]="isPanelCollapsed">
          <!-- Search Section -->
          <div class="search-section">
            <form [formGroup]="searchForm">
              <ava-textbox
                placeholder='Search "Agents"'
                hoverEffect="glow"
                pressedEffect="solid"
                formControlName="agentFilter"
              >
                <ava-icon
                  slot="icon-start"
                  iconName="search"
                  [iconSize]="16"
                  iconColor="var(--color-brand-primary)"
                >
                </ava-icon>
              </ava-textbox>
            </form>
          </div>

          <!-- Agent List -->
          <div class="agents-list">
            <!-- Agent Items -->
            <div
              *ngFor="let agent of availableAgents"
              class="agent-item"
              draggable="true"
              (dragstart)="onDragStart($event, agent)"
            >
              <div class="agent-header">
                <div class="agent-icon-box">
                  <ava-icon
                    iconName="User"
                    iconSize="20"
                    iconColor="#ffffff"
                  ></ava-icon>
                </div>
                <h4 class="agent-name">{{ agent.name }}</h4>
                <div class="agent-count">
                  <ava-icon
                    iconName="Users"
                    iconSize="16"
                    iconColor="#9CA3AF"
                  ></ava-icon>
                  <span class="count-text">120</span>
                </div>
              </div>
              <p class="agent-description">{{ agent.description }}</p>

              <!-- Agent Tags -->
              <div
                class="agent-tags"
                *ngIf="agent.capabilities && agent.capabilities.length > 0"
              >
                <span
                  class="agent-tag"
                  *ngFor="let capability of agent.capabilities"
                  >{{ capability }}</span
                >
              </div>

              <!-- Preview button -->
              <div class="agent-actions">
                <ava-button
                  label="Preview"
                  size="small"
                  [pill]="true"
                  variant="secondary"
                  (userClick)="onItemPreview(agent)"
                  class="preview-btn"
                >
                </ava-button>
              </div>
            </div>

            <!-- No results message -->
            <div
              class="no-results-message"
              *ngIf="availableAgents.length === 0"
            >
              <div class="no-results-content">
                <ava-icon
                  iconName="Search"
                  iconSize="24"
                  iconColor="#9CA3AF"
                ></ava-icon>
                <p>No agents found matching your criteria</p>
              </div>
            </div>
          </div>

          <!-- Create New Agent Button -->
          <div class="create-agent-section">
            <ava-button
              label="Create New Agent"
              variant="primary"
              size="large"
              iconName="Plus"
              iconColor="white"
              (userClick)="onCreateNewAgent()"
              [width]="'100%'"
            >
            </ava-button>
          </div>
        </div>
      </div>

      <!-- Action Buttons Section - Positioned on the right -->
      <div class="action-buttons-section">
        <div class="action-group">
          <button class="action-btn" title="Undo">
            <ava-icon iconName="Undo" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
          </button>
          <button class="action-btn" title="Redo">
            <ava-icon iconName="Redo" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
          </button>
          <button class="action-btn" title="Refresh">
            <ava-icon iconName="RefreshCw" iconSize="16" iconColor="var(--text-secondary)"></ava-icon>
          </button>
          <button class="run-btn" title="Execute Workflow">
            <ava-icon iconName="Play" iconSize="16" iconColor="white"></ava-icon>
            Execute
          </button>
        </div>
      </div>

      <!-- Workflow Editor Canvas -->
      <div class="editor-canvas">
        <app-canvas-board
          [nodes]="canvasNodes"
          [edges]="canvasEdges"
          [navigationHints]="navigationHints"
          [fallbackMessage]="workFlowLabels.fallbackMessage"
          [primaryButtonText]="workFlowLabels.execute"
          (canvasDropped)="onCanvasDropped($event)"
          (nodeSelected)="onNodeSelected($event)"
          (nodeMoved)="onNodeMoved($event)"
          (nodeRemoved)="onDeleteNode($event)"
          (connectionStarted)="onStartConnection($event)"
          (connectionCreated)="onConnectionCreated($event)"
          (undoAction)="onUndo()"
          (redoAction)="onRedo()"
          (resetAction)="onReset()"
          (primaryButtonClicked)="onExecute()"
          (stateChanged)="onCanvasStateChanged($event)"
          [showLeftActions]="true"
          [showHeaderInputs]="true"
          [inputFieldsConfig]="inputFieldsConfig"
          [agentDetailNameControl]="getControl('name')"
          [agentDetailControl]="getControl('description')"
        >
          <!-- Node template for rendering agent nodes -->
          <ng-template
            #nodeTemplate
            let-node
            let-selected="selected"
            let-onDelete="onDelete"
            let-onMove="onMove"
            let-onSelect="onSelect"
            let-onStartConnection="onStartConnection"
          >
            <app-agent-node
              [node]="node"
              [selected]="selected"
              (deleteNode)="onDelete($event)"
              (moveNode)="onMove($event)"
              (nodeSelected)="onSelect($event)"
              (startConnection)="onStartConnection($event)"
              (nodePositionChanged)="updateNodePosition($event)"
            >
            </app-agent-node>
          </ng-template>

          <div
            info
            class="model-info"
            *ngIf="
              getControl('enableManagerLLM').value &&
              getControl('modelDeploymentName')?.value as modelValue
            "
          >
            You are currently using {{ modelValue }}
          </div>

          <ng-container actionLeft>
            <div class="llm-toggle-container">
              <div class="toggle-container">
                <span class="toggle-label">{{ workFlowLabels.managerLLMToggleLabel }}</span>
                <ava-toggle
                  size="small"
                  (checkedChange)="onToggleChnage($event)"
                  position="left"
                  [animation]="true"
                  [checked]="!!getControl('enableManagerLLM').value"
                >
                </ava-toggle>
              </div>
            </div>
          </ng-container>
        </app-canvas-board>
      </div>
    </div>
  </div>
</div>

<ava-popup
  [show]="showWarningPopup"
  [showTitle]="false"
  [showHeaderIcon]="false"
  [showInlineMessage]="true"
  [inlineIconName]="'info'"
  [inlineIconSize]="48"
  [inlineIconColor]="'#007bff'"
  [inlineMessage]="'Warning!'"
  [message]="'Please provide the workflow name before executing the workflow..'"
  [showClose]="true"
  [showCancel]="true"
  [cancelButtonLabel]="'Okay'"
  [cancelButtonVariant]="'primary'"
  [popupWidth]="'400px'"
  (closed)="showWarningPopup = false"
>
</ava-popup>
