<div class="approval">
    <div class="approval-left-screen" [class.quick-actions-expanded]="quickActionsExpanded">
        <!-- <ava-sidebar class="basic-sidebar-theme" width="280px" collapsedWidth="65px" height="500px"
            [class.basic-collapsed]="isBasicCollapsed" (collapseToggle)="onBasicCollapseToggle($event)">  
            <div slot="content">
                <div class="basic-nav-section">
                    <div *ngFor="let item of basicSidebarItems" class="basic-nav-item" [class.basic-active]="item.active"
                        (click)="onBasicItemClick(item)">
                        <ava-icon [iconName]="item.icon" class="basic-nav-icon"></ava-icon>
                        <span class="basic-nav-text">{{ item.text }}</span>
                    </div>
                </div>
            </div>
        </ava-sidebar> -->
        <div class="quick-actions-wrapper" [class.expanded]="quickActionsExpanded">
            <div class="quick-actions-toggle" (click)="toggleQuickActions()">
                <div class="toggle-button">
                    <svg [class.rotate]="quickActionsExpanded" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M5 12h14M12 5l7 7-7 7" stroke="#6566CD" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </div>
            </div>

            <!-- Expanded view with text labels -->
            <div class="quick-actions-content" *ngIf="quickActionsExpanded">
                <div class="action-buttons">
                    <button class="action-button" (click)="loadReviews('Agents')">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="'Agents'" width="24" height="24" />
                        </div>
                        <span class="action-label">Agents</span>
                    </button>
                    
                    <button class="action-button" (click)="loadReviews('Workflows')">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="'Workflows'" width="24" height="24" />
                        </div>
                        <span class="action-label">Workflows</span>
                    </button>
                    
                    <button class="action-button" (click)="loadReviews('Tools')">
                        <div class="action-icon">
                            <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="'Tools'" width="24" height="24" />
                        </div>
                        <span class="action-label">Tools</span>
                    </button>
                </div>
            </div>

            <!-- Collapsed view with icons only -->
            <div class="quick-actions-icons" *ngIf="!quickActionsExpanded">
                <button class="icon-button" (click)="loadReviews('Agents')" [title]="'Agents'">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_agents' + '.svg'" [alt]="'Agents'" width="24"
                            height="24" />
                    </div>
                </button>
                 <button class="icon-button" (click)="loadReviews('Workflows')" [title]="'Workflows'">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_workflows' + '.svg'" [alt]="'Workflows'" width="24"
                            height="24" />
                    </div>
                </button>
                <button class="icon-button" (click)="loadReviews('Tools')" [title]="'Tools'">
                    <div class="icon-wrapper">
                        <img [src]="'svgs/icons/' + 'awe_tools' + '.svg'" [alt]="'Tools'" width="24"
                            height="24" />
                    </div>
                </button>
            </div>
        </div>
    </div>
    
    <div class="approval-right-screen">
        <div class="approval-title-filter">
            <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="'Total Approval'" [value]="totalApprovals"
                [description]="currentTab + ' which are requested for approval'">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'shield-alert'" [title]="'Approved'" [value]="totalApprovedApprovals"
                [description]="currentTab + ' which are approved'">
            </ava-text-card>
            <ava-text-card [type]="'default'" [iconName]="'hourglass'" [title]="'Pending'" [value]="totalPendingApprovals"
                [description]="'All ' + currentTab + ' awaiting approval'">
            </ava-text-card>
        </div>
        
    <div class="filter-section">
        <div class="search-bars">
            <ava-dropdown dropdownTitle="All Status" [options]="options" [search]="true"
                (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
            <ava-dropdown dropdownTitle="All Priority" [options]="options" [search]="true"
                (selectionChange)="onSelectionChange($event)">
            </ava-dropdown>
            <!-- <ava-button label="Bulk Approve" (userClick)="uClick(1)" variant="primary" size="large" state="default"
                iconPosition="left"></ava-button> -->
        </div>
        <div class="textbox section">
            <div>
                <form [formGroup]="searchForm">
                    <ava-textbox placeholder="Search..." [(ngModel)]="searchValue" formControlName="search">
                        <ava-icon slot="icon-start" iconName="search" [iconSize]="16" iconColor="var(--color-brand-primary)">
                        </ava-icon>
                    </ava-textbox>
                </form>
                <ava-button label="Primary" variant="primary" iconName="calendar-days" iconPosition="only"
                gradient="linear-gradient(118deg, #7FC2EB 0%, #5B92EA 89.27%)" (userClick)="uClick($event)"></ava-button>
            </div>
        </div>
    </div>
        
        <div class="approval-card-section">
            <div class="approval-card-header">
                All - {{totalRecords}} {{currentTab}}
            </div>
            <ng-template let-i="index" let-label="label" #footerTemplate>
                <div class="footer-content">
                  <ng-container *ngIf="label">
                    <div class="footer-left">
                      <ava-icon iconSize="20" [iconName]="label.iconName"></ava-icon>
                      <span>{{label.status}}</span>
                    </div>
                  </ng-container>
                    <div class="footer-right">
                        <ava-button label="Test" (userClick)="uClick(i)" variant="secondary" size="medium" state="default"
                            iconName="play" iconPosition="left"></ava-button>
                        <ava-button label="Sendback" (userClick)="rejectApproval(i)" variant="secondary" size="medium" state="default"
                            iconName="move-left" iconPosition="left"></ava-button>
                        <ava-button label="Approve" (userClick)="approveApproval(i)" variant="primary" size="medium" state="default"
                            iconName="Check" iconPosition="left"></ava-button>
                    </div>
                </div>
            </ng-template>
            <ava-approval-card height="800" [contentTemplate]="footerTemplate" [cardData]="consoleApproval"
            [contentsBackground]="'#ffffff'" [cardContainerBackground]="'#F8F8F8'">
            </ava-approval-card>
        </div>
    </div>
</div>

<ava-popup
  [show]="showToolApprovalPopup"
  title="Confirm Approval?"
  [message]="'You are about to approve this ' + (currentTab) + 'It will be active and available in' + (currentTab) + 'catalogue for users to execute.'"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="'Approve'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="handleApproval()"
  (cancel)="showToolApprovalPopup=false"
  (closed)="showToolApprovalPopup=false"
>
</ava-popup>

<ava-popup messageAlignment="center" [show]="showInfoPopup"
    title="SUCCESS!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-check" iconColor="green" [showClose]="true" (closed)="handleInfoPopup()">
</ava-popup>

<ava-confirmation-popup [show]="showFeedbackPopup" title="Confirm Send Back"
    message="This {{currentTab}} will be send back for corrections and modification. Kindly comment what needs to be done."
    confirmationLabel="Send Back" (closed)="showFeedbackPopup = false" (confirm)="handleRejection($event)">
</ava-confirmation-popup>

<ava-popup messageAlignment="center" [show]="showErrorPopup"
    title="FAILED!" message={{infoMessage}} [showHeaderIcon]="true"
    headerIconName="circle-x" iconColor="red" [showClose]="true" (closed)="handleInfoPopup()" >
</ava-popup>