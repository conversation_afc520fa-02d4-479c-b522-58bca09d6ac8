import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { TokenStorageService } from '../../../../../../../shared/auth/services/token-storage.service';

@Injectable({
  providedIn: 'root'
})
export class AgentPlaygroundService {
  private apiServiceUrl = environment.consoleInstructionApi;
  private defaultStr = '/ava/force/individualAgent/execute';
  private convStr = '/ava/force/individualAgent/execute/chat';

  constructor(
    private http: HttpClient,
    private tokenStorage: TokenStorageService
  ) { }

  private getUserSignature() {
    const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';
    return userSignature;
  }

  /**
   * Generate prompt using individual agent API
   * @param data - The prompt data or conversation data
   * @param mode - The mode for execution (agent mode)
   * @param isConvChecked - Whether to use conversation mode
   * @param isTemplateChecked - Whether to use template mode
   * @param attachment - Array of attachments
   * @param useCaseIdentifier - Use case identifier
   * @param fileContents - Optional file contents to include in message
   * @returns Observable with API response
   */
  public generatePrompt(
    data: any,
    mode: string = '',
    isConvChecked: boolean = false,
    isTemplateChecked: boolean = false,
    attachment: string[] = [],
    useCaseIdentifier: string = '',
    fileContents: string = ''
  ): Observable<any> {
    let payload: any = {
      mode: mode,
      useCaseIdentifier: useCaseIdentifier,
      userSignature: this.getUserSignature(),
    };

    let queryString = '';

    if (isConvChecked) {
      // Conversational mode - send conversations array
      let conversationData = Array.isArray(data) ? data : [{ content: data, role: 'user' }];

      // If file contents exist, append to the last user message
      if (fileContents && conversationData.length > 0) {
        const lastMessage = conversationData[conversationData.length - 1];
        if (lastMessage.role === 'user') {
          lastMessage.content = `${lastMessage.content}\n\nFile Contents:\n${fileContents}`;
        }
      }

      payload.conversations = conversationData;
      queryString = this.convStr;
    } else {
      // Non-conversational mode - send prompt with promptOverride
      let promptText = typeof data === 'string' ? data : data.content || data;

      // If file contents exist, append to the prompt
      if (fileContents) {
        promptText = `${promptText}\n\nFile Contents:\n${fileContents}`;
      }

      payload.prompt = promptText;
      payload.promptOverride = true; // Always true for non-conversational mode

      // Add attachments if present
      if (attachment?.length) {
        payload['image'] = attachment;
      }
      queryString = this.defaultStr;
    }

    const headers = { 
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };
    
    const url = `${this.apiServiceUrl}${queryString}`;

    return this.http.post(url, payload, headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Parse file content and extract text
   * @param formData - FormData containing files to parse
   * @returns Observable with parsed file content
   */
  public getFileToContent(formData: FormData): Observable<any> {
    const headers = { 
      headers: new HttpHeaders({
        // Don't set Content-Type for FormData, let browser set it with boundary
      })
    };
    
    const url = `${this.apiServiceUrl}/ava/force/contents`;
    
    return this.http.post(url, formData, headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /**
   * Upload files for processing
   * @param files - Array of files to upload
   * @returns Observable with upload response
   */
  public uploadFiles(files: File[]): Observable<any> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const headers = { 
      headers: new HttpHeaders({
        // Don't set Content-Type for FormData
      })
    };
    
    const url = `${this.apiServiceUrl}/ava/force/files/upload`;
    
    return this.http.post(url, formData, headers).pipe(
      map((response: any) => {
        return response;
      })
    );
  }
}
