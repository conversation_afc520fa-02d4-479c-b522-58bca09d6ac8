import { Component, Input, Output, EventEmitter, ElementRef, ViewChild, AfterViewInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule, CdkDragEnd, CdkDrag } from '@angular/cdk/drag-drop';
import { IconComponent } from "@ava/play-comp-library";

export interface BuildAgentNodeData {
  id: string;
  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';
  name: string;
  icon?: string;
  position: { x: number; y: number };
  originalToolData?: any; // Store the original tool data for configuration purposes
}

export interface ExecuteNodeData {
  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';
  nodes: BuildAgentNodeData[]; // All nodes of this type
  position: { x: number; y: number };
}

@Component({
  selector: 'app-build-agent-node',
  standalone: true,
  imports: [CommonModule, DragDropModule, IconComponent],
  templateUrl: './build-agent-node.component.html',
  styleUrls: ['./build-agent-node.component.scss']
})
export class BuildAgentNodeComponent implements AfterViewInit, OnChanges {
  @ViewChild('nodeElement') nodeElement!: ElementRef;
  @ViewChild('dragRef') cdkDrag!: CdkDrag;

  @Input() node: any;
  @Input() selected: boolean = false;
  @Input() mouseInteractionsEnabled: boolean = true;
  @Input() canvasMode: string = 'build'; // 'build' or 'execute'
  @Input() executeNodeData?: ExecuteNodeData; // For execute mode consolidated nodes

  @Output() deleteNode = new EventEmitter<string>();
  @Output() moveNode = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();
  @Output() nodeSelected = new EventEmitter<string>();
  @Output() nodeDoubleClicked = new EventEmitter<string>();
  @Output() nodePositionChanged = new EventEmitter<{nodeId: string, position: {x: number, y: number}}>();
  @Output() startConnection = new EventEmitter<{nodeId: string, handleType: 'source' | 'target', event: MouseEvent}>();

  isDragging: boolean = false;
  showTooltip: boolean = false;

  private readonly nodeConfig = {
    prompt: { icon: 'FileText', useAsset: false },
    model: { icon: 'Box', useAsset: false },
    knowledge: { icon: 'BookOpen', useAsset: false },
    tool: { icon: 'Wrench', useAsset: false },
    guardrail: { icon: 'Swords', useAsset: false }
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['node']?.currentValue) {
      const currentNode = changes['node'].currentValue;
      const previousNode = changes['node'].previousValue;

      if (this.hasPositionChanged(currentNode, previousNode)) {
        this.updateCdkDragPosition(currentNode.data.position);
      }
    }
  }

  private hasPositionChanged(currentNode: any, previousNode: any): boolean {
    if (!currentNode?.data?.position) return false;
    if (!previousNode?.data?.position) return true;

    const current = currentNode.data.position;
    const previous = previousNode.data.position;
    return current.x !== previous.x || current.y !== previous.y;
  }

  ngAfterViewInit(): void {
    const nodeData = this.currentNodeData;
    if (nodeData) {
      setTimeout(() => this.nodePositionChanged.emit({
        nodeId: nodeData.id,
        position: nodeData.position
      }), 0);
    }
  }

  private updateCdkDragPosition(position: { x: number; y: number }): void {
    if (this.isDragging) return;

    // Small delay to prevent flickering while maintaining sync
    setTimeout(() => {
      try {
        if (this.cdkDrag) {
          this.cdkDrag.reset();
          this.cdkDrag.setFreeDragPosition(position);
        }

        if (this.nodeElement?.nativeElement) {
          this.nodeElement.nativeElement.style.transform = `translate3d(${position.x}px, ${position.y}px, 0px)`;
        }
      } catch (error) {
        console.warn('Error updating CDK drag position:', error);
        if (this.nodeElement?.nativeElement) {
          const element = this.nodeElement.nativeElement;
          element.style.position = 'absolute';
          element.style.left = `${position.x}px`;
          element.style.top = `${position.y}px`;
          element.style.transform = '';
        }
      }
    }, 5);
  }

  get currentNodeData(): BuildAgentNodeData {
    return this.node?.data || {};
  }

  get isExecuteMode(): boolean {
    return this.canvasMode === 'execute';
  }

  get executeTooltipText(): string {
    if (!this.isExecuteMode || !this.executeNodeData) return '';

    const nodeNames = this.executeNodeData.nodes.map(node => node.name);
    if (nodeNames.length === 1) {
      return nodeNames[0];
    } else if (nodeNames.length > 1) {
      const typeLabel = this.executeNodeData.type === 'knowledge' ? 'knowledge bases' : `${this.executeNodeData.type}s`;
      return `${nodeNames.length} ${typeLabel}:\n${nodeNames.join('\n')}`;
    }
    return '';
  }

  get nodePosition(): { x: number; y: number } {
    if (this.isExecuteMode && this.executeNodeData) {
      return this.executeNodeData.position || { x: 0, y: 0 };
    }
    return this.currentNodeData.position || { x: 0, y: 0 };
  }

  get isCurrentlySelected(): boolean {
    return this.selected;
  }

  get currentConfig() {
    if (this.isExecuteMode && this.executeNodeData) {
      return this.nodeConfig[this.executeNodeData.type] || this.nodeConfig.prompt;
    }
    const nodeData = this.currentNodeData;
    return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;
  }

  get shouldUseAssetIcon(): boolean {
    const config = this.currentConfig;
    return config.useAsset || false;
  }

  get iconName(): string {
    const config = this.currentConfig;

    if (this.isExecuteMode && this.executeNodeData) {
      // In execute mode, always use Lucide icons
      return config.icon;
    }

    const nodeData = this.currentNodeData;

    // For asset icons (prompts), use the node's icon or config icon
    if (config.useAsset) {
      return nodeData.icon || config.icon;
    }

    // For Lucide icons, always use the config icon (which contains the correct Lucide icon name)
    // Ignore any asset paths that might be in nodeData.icon for non-prompt types
    return config.icon;
  }

  onImageError(event: any): void {
    // Fallback to the default icon if the image fails to load
    const fallbackIcon = 'assets/images/build.png';
    event.target.src = fallbackIcon;
    console.warn(`Failed to load icon for ${this.currentNodeData?.type} node, using fallback:`, fallbackIcon);
  }

  onNodeClick(): void {
    if (this.isExecuteMode) {
      // In execute mode, don't emit selection events
      return;
    }
    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {
      this.nodeSelected.emit(this.currentNodeData.id);
    }
  }

  onNodeDoubleClick(): void {
    if (this.isExecuteMode) {
      // In execute mode, don't emit double-click events
      return;
    }
    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {
      this.nodeDoubleClicked.emit(this.currentNodeData.id);
    }
  }

  onMouseEnter(): void {
    if (this.isExecuteMode && this.executeTooltipText) {
      this.showTooltip = true;
    }
  }

  onMouseLeave(): void {
    if (this.isExecuteMode) {
      this.showTooltip = false;
    }
  }

  onDeleteClick(event: Event): void {
    event.stopPropagation();
    if (this.currentNodeData.id) {
      this.deleteNode.emit(this.currentNodeData.id);
    }
  }

  onDragEnded(event: CdkDragEnd): void {
    if (!this.mouseInteractionsEnabled) return;

    this.isDragging = false;
    const nodeData = this.currentNodeData;
    if (!nodeData?.id) return;

    const transform = event.source.getFreeDragPosition();
    const safePosition = {
      x: Math.max(0, transform.x),
      y: Math.max(0, transform.y)
    };

    this.moveNode.emit({ nodeId: nodeData.id, position: safePosition });
    this.nodePositionChanged.emit({ nodeId: nodeData.id, position: safePosition });
  }

  onNodeMouseDown(event: MouseEvent): void {
    if (this.mouseInteractionsEnabled) {
      // Check for connection starting gesture (Ctrl+click or right-click)
      if (event.ctrlKey || event.button === 2) {
        // Start connection from this node
        event.preventDefault();
        event.stopPropagation();

        // Make sure position is up to date before starting connection (like workflow editor)
        this.nodePositionChanged.emit({
          nodeId: this.currentNodeData.id,
          position: this.currentNodeData.position
        });

        this.startConnection.emit({
          nodeId: this.currentNodeData.id,
          handleType: 'source',
          event: event
        });
        return;
      }

      this.isDragging = true;
      this.onNodeClick();
    }
  }


}
