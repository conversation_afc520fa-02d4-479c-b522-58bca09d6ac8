// Build Agent Node Styles
.build-agent-node {
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.1s ease; // Only transition opacity for smooth visual feedback
  display: inline-block;
  margin: 0;
  padding: 0;
  box-sizing: border-box;

  // Default build mode dimensions
  width: auto;
  height: 48px;

  &.selected {
    z-index: 10;
  }

  &.dragging {
    z-index: 20;
    opacity: 0.9; // Better visibility during drag
    transition: none; // No transitions during drag
  }

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  // Execute mode styling
  &.execute-mode {
    width: 55px;
    height: 55px;
    background: white;
    border-radius: 40px;
    border: 7px solid white; // White border around the icon
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    position: relative;

  }

  // Build Mode Styling - White pill with gradient icon
  .node-content-build {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: auto; // Changed to auto to accommodate padding
    padding: 10px; // Added 10px padding on all sides
    position: relative;
    transition: none; // Remove transition for smooth movement
    min-width: fit-content;

    // Single connection point behind the icon
    &::before {
      content: '';
      position: absolute;
      left: 34px; // Adjusted for 10px padding + 24px icon center
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      background: #9CA3AF;
      border-radius: 50%;
      z-index: 0;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    // Gradient icon section
    .node-icon-section {
      width: 40px; // Reduced from 48px to account for padding
      height: 40px; // Reduced from 48px to account for padding
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      z-index: 2;
      position: relative;

      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
        filter: brightness(0) invert(1); // Make asset images white
      }

      // Lucide icons are already white via the color attribute
    }

    // Label section
    .node-label-section {
      padding: 0 0 0 12px; // Removed right padding since we have 10px padding on container
      display: flex;
      align-items: center;
      height: 100%;

      .node-label {
        font-size: 14px;
        font-weight: 500;
        color: #1F2937;
        white-space: nowrap;
        line-height: 1;
      }
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: box-shadow 0.1s ease; // Only transition hover effects

      &::before {
        opacity: 1;
        transition: opacity 0.1s ease; // Only transition connection point
      }
    }
  }

  // Execute Mode Styling (Circular Icon Only - same as build mode icon section)
  .node-content-execute {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    border: 7px solid white; // White border around the icon
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: none; // Remove transition to prevent flickering


    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
      filter: brightness(0) invert(1);
    }

    lucide-icon {
      color: white;
    }
  }

  // Node type specific gradient colors for build mode icons
  &[data-node-type="prompt"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #8BE1EB 0%, #028697 100%);
  }

  &[data-node-type="model"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #FEC47E 0%, #DB770C 100%);
  }

  &[data-node-type="knowledge"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #F598B7 0%, #D41B5A 100%);
  }

  &[data-node-type="tool"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #81D39F 0%, #179EAD 100%);
  }

  &[data-node-type="guardrail"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #B9A4DE 0%, #5E35A7 100%);
  }

  // Execute mode colors - matching the reference image
  &[data-node-type="prompt"] .node-content-execute {
    background: linear-gradient(180deg, #8BE1EB 0%, #028697 100%);
  }

  &[data-node-type="model"] .node-content-execute {
    background: linear-gradient(180deg, #FEC47E 0%, #DB770C 100%);
  }

  &[data-node-type="knowledge"] .node-content-execute {
    background: linear-gradient(180deg, #F598B7 0%, #D41B5A 100%);
  }

  &[data-node-type="tool"] .node-content-execute {
    background: linear-gradient(180deg, #81D39F 0%, #179EAD 100%);
  }

  &[data-node-type="guardrail"] .node-content-execute {
    background: linear-gradient(180deg, #B9A4DE 0%, #5E35A7 100%);
  }

  // Selected state styling
  &.selected .node-content-build {
    box-shadow: 0 0 0 2px #3B82F6;
  }

  &.selected .node-content-execute {
    box-shadow: 0 0 0 2px #3B82F6;
  }

  // Delete button styling
  .delete-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #EF4444;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.2s ease;

    &:hover {
      background: #DC2626;
      transform: scale(1.1);
    }

    svg {
      width: 10px;
      height: 10px;
    }
  }

  &:hover .delete-btn,
  &.selected .delete-btn {
    display: flex;
  }

  // Hide all connection points - we only use the single point behind icon
  .connection-point {
    display: none;
  }

  // Execute mode tooltip styling
  .execute-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: pre-line;
    z-index: 1000;
    margin-bottom: 8px;
    min-width: max-content;
    max-width: 200px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    // Arrow pointing down
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 5px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.9);
    }
  }
}
