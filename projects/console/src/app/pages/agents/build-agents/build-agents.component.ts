import {
  Component,
  <PERSON><PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import {
  ButtonComponent,
  TabsComponent,
  AvaTab,
  DropdownOption,
  PopupComponent,
  AvaTextboxComponent,
  IconComponent,
} from '@ava/play-comp-library';
import {
  CanvasBoardComponent,
  CanvasNode,
  CanvasEdge,
} from '../../../shared/components/canvas-board/canvas-board.component';
import {
  BuildAgentNodeComponent,
  BuildAgentNodeData,
  ExecuteNodeData,
} from './components/build-agent-node';
import { PlaygroundComponent } from '../../../shared/components/playground/playground.component';
import { ChatMessage } from '../../../shared/components/chat-window';
import { ToolExecutionService } from '../../../shared/services/tool-execution/tool-execution.service';
import {
  Subscription,
  Subject,
  takeUntil,
  switchMap,
  finalize,
  catchError,
  of,
} from 'rxjs';

import { PromptsService } from '../../../shared/services/prompts.service';
import { ToolsService } from '../../../shared/services/tools.service';
import { AgentServiceService } from '../services/agent-service.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AgentPlaygroundService } from './services/agent-playground.service';

import { CustomTabsComponent, CustomTab } from '../../../shared/components';

interface ToolItem {
  id: string;
  name: string;
  description: string;
  icon: string;
  type: 'tool' | 'model' | 'knowledge' | 'prompt' | 'guardrail';
  // Additional properties that may be present after fetching detailed data
  [key: string]: any;
}

@Component({
  selector: 'app-build-agents',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    CanvasBoardComponent,
    BuildAgentNodeComponent,
    PlaygroundComponent,
    AvaTextboxComponent,
    IconComponent,
    PopupComponent,
    CustomTabsComponent,
  ],
  templateUrl: './build-agents.component.html',
  styleUrls: ['./build-agents.component.scss'],
})
export class BuildAgentsComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild(CanvasBoardComponent) canvasBoardComponent!: CanvasBoardComponent;

  private destroy$ = new Subject<void>();
  activeTab: string = 'prompts';
  searchQuery: string = '';
  searchForm!: FormGroup;

  // Sidebar collapse functionality
  isSidebarCollapsed: boolean = false;

  // Preview functionality
  showPreview: boolean = false;
  selectedItem: any = null;
  previewData: any = null;
  isLoadingPreview: boolean = false;
  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  buildAgentNodes: BuildAgentNodeData[] = [];
  executeNodes: ExecuteNodeData[] = []; // Consolidated nodes for execute mode
  selectedNodeId: string | null = null;
  currentAgentType: string = 'individual'; // Default to individual, can be 'individual' or 'collaborative'

  // Agent mode properties
  currentMode: 'create' | 'view' | 'edit' | 'duplicate' = 'create';
  currentAgentId: string | null = null;
  isFieldsDisabled: boolean = false;
  isDuplicateMode: boolean = false;

  // Success/Error/Warning popup properties
  showSuccessPopup: boolean = false;
  showErrorPopup: boolean = false;
  showWarningPopup: boolean = false;
  popupMessage: string = '';
  popupTitle: string = '';

  // Node limits based on agent type
  private readonly nodeLimits = {
    individual: {
      prompt: 1, // mandatory, max 1
      model: 1, // mandatory, max 1
      knowledge: -1, // unlimited
      guardrail: -1, // unlimited
    },
    collaborative: {
      prompt: 1, // mandatory, max 1
      model: 1, // mandatory, max 1
      knowledge: -1, // unlimited
      tool: -1, // unlimited
    },
  };

  tabs: AvaTab[] = []; // Will be configured based on agent type

  // Custom tabs for the custom tabs component
  get customTabs(): CustomTab[] {
    return this.tabs.map((tab) => ({
      label: tab.label,
      value: tab.value,
      icon: tab.icon,
      disabled: false,
    }));
  }

  public allToolItems: { [key: string]: ToolItem[] } = {
    prompts: [], // Will be populated from API with filtering
    models: [], // Will be populated from API
    knowledge: [], // Will be populated from API
    tools: [], // Will be populated from API
    guardrails: [], // Will be populated from API
  };

  // Canvas configuration
  navigationHints: string[] = [
    'Alt + Drag to pan canvas',
    'Mouse wheel to zoom',
    'Space to reset view',
  ];

  // Execution mode properties
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;
  promptOptions: DropdownOption[] = [];
  private executionSubscription?: Subscription;

  // Agent mode properties
  isEditMode: boolean = false;
  isViewMode: boolean = false;
  isNewAgent: boolean = true;
  agentConfigIds: Map<string, number> = new Map(); // Store configId for edit operations

  // Get dynamic button text based on current mode
  get primaryButtonText(): string {
    if (this.isExecuteMode) {
      return 'Run'; // When in execute mode, show "Run"
    }

    if (this.isViewMode) {
      return 'Execute'; // When viewing existing agent, show "Execute"
    }

    if (this.isEditMode) {
      return 'Update'; // When editing existing agent, show "Update"
    }

    if (this.isDuplicateMode) {
      return 'Save'; // When duplicating agent, show "Save"
    }

    return 'Save'; // When creating new agent, show "Save and Run"
  }

  // Agent data properties for saving
  agentName: string = '';
  agentDetail: string = '';
  agentCode: string = ''; // Store the agent code (e.g., BUGBUSTERCHATBOT) for API calls
  agentMetadata: {
    org: string;
    domain: string;
    project: string;
    team: string;
  } = { org: '', domain: '', project: '', team: '' };

  // Helper method to build organization path from metadata
  private buildOrganizationPath(): string {
    const { org, domain, project, team } = this.agentMetadata;
    if (org && domain && project && team) {
      return `@${org}@${domain}@${project}@${team}`;
    }
    // Fallback to hardcoded path if metadata is not available
    return '@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';
  }

  // Cache for labels API response to avoid multiple calls
  private labelsCache: any = null;

  // Agent Playground Properties
  public agentPlaygroundForm!: FormGroup;
  public selectedPrompt: string = 'default';
  public selectedAgentMode: string = '';
  public selectedUseCaseIdentifier: string = '';
  public agentChatPayload: any[] = [];
  public agentFilesUploadedData: any[] = [];
  public agentAttachment: string[] = [];
  public isAgentPlaygroundLoading: boolean = false;
  public agentPlaygroundDestroy: Subject<boolean> = new Subject<boolean>();

  // Modal configuration for API response popup
  public responseModalConfig: any = {
    id: 'responseModal',
    type: 'dialog',
    title: 'Agent Response',
    closeButton: false,
    primaryButtonConfig: {
      text: 'OK',
      buttonType: 'primary',
      buttonSize: 'medium',
      imageUrl: '',
      linkURL: '',
    },
  };

  // Modal state
  public isResponseModalOpen: boolean = false;
  public modalMessage: string = '';
  public isModalError: boolean = false;

  // Auto-selected agent from card click
  public autoSelectedAgentFromCard: any = null;

  // Test property for space input testing
  public testSpaceInput: string = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private cdr: ChangeDetectorRef,
    private toolsService: ToolsService,
    private agentService: AgentServiceService,
    private promptsService: PromptsService,
    private tokenStorage: TokenStorageService,
    private agentPlaygroundService: AgentPlaygroundService,
    private formBuilder: FormBuilder,
  ) {
    // Initialize agent playground form
    this.agentPlaygroundForm = this.formBuilder.group({
      isConversational: [true],
      isUseTemplate: [false],
    });

    // Initialize search form
    this.searchForm = this.formBuilder.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    // Get agent type from route parameters
    this.route.params.subscribe((params) => {
      const type = params['type'];
      if (type === 'individual' || type === 'collaborative') {
        this.currentAgentType = type;
        this.configureTabsForAgentType();
        this.loadDataForAgentType();
      } else {
        this.currentAgentType = 'individual';
        this.configureTabsForAgentType();
        this.loadDataForAgentType();
      }
    });

    // Add some test data to ensure scrolling works
    setTimeout(() => {
      if (this.allToolItems['prompts'].length === 0) {
        this.allToolItems['prompts'] = Array.from({ length: 20 }, (_, i) => ({
          id: `test-prompt-${i}`,
          name: `Test Prompt ${i + 1}`,
          description: `This is a test prompt description for prompt ${i + 1}. It has a longer description to make the content scrollable.`,
          icon: 'assets/images/prompt.png',
          type: 'prompt' as const,
        }));
      }
    }, 1000);

    // Check if agent ID is passed in query params (from catalogue)
    this.route.queryParams.subscribe((params) => {
      if (params['id']) {
        this.currentAgentId = params['id'];

        // Determine mode based on query parameters
        const mode = params['mode']; // 'view', 'edit', 'execute', or undefined for create

        if (mode === 'view') {
          this.isViewMode = true;
          this.isEditMode = false;
          this.isNewAgent = false;
          this.isExecuteMode = false;
          this.isFieldsDisabled = true; // Disable fields in view mode
        } else if (mode === 'edit') {
          this.isEditMode = true;
          this.isViewMode = false;
          this.isNewAgent = false;
          this.isExecuteMode = false;
          this.isFieldsDisabled = false; // Enable fields in edit mode
        } else if (mode === 'execute') {
          // Execute mode - load agent and immediately open playground
          this.isExecuteMode = true;
          this.isViewMode = false;
          this.isEditMode = false;
          this.isNewAgent = false;
          this.isDuplicateMode = false;
          this.isFieldsDisabled = true; // Disable fields in execute mode
          this.showChatInterface = true; // Show playground immediately
        } else if (mode === 'duplicate') {
          // Duplicate mode - load agent data but clear name field for new entry
          this.isDuplicateMode = true;
          this.isNewAgent = false; // We're loading existing data
          this.isEditMode = false;
          this.isViewMode = false;
          this.isExecuteMode = false;
          this.isFieldsDisabled = false; // Enable fields for editing
          this.agentConfigIds.clear(); // Clear config IDs to ensure new agent creation
          // Note: currentAgentId is kept to load the original agent data, but cleared after loading
        } else {
          // Default to create mode
          this.isNewAgent = true;
          this.isEditMode = false;
          this.isViewMode = false;
          this.isExecuteMode = false;
          this.isDuplicateMode = false;
          this.isFieldsDisabled = false;
        }

        this.loadAgentData(params['id']);
      } else {
        // No agent ID - creating new agent
        this.isNewAgent = true;
        this.isEditMode = false;
        this.isViewMode = false;
        this.isFieldsDisabled = false;
        this.currentAgentId = null;
      }
    });

    this.activeTab = 'prompts';

    // Subscribe to search form changes
    this.searchForm
      .get('search')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        this.searchQuery = value || '';
      });
  }

  ngAfterViewInit(): void {
    // Trigger change detection after view init to ensure filtered tools are updated
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    // Clean up all subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up execution subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }

    // Clean up agent playground subscription
    this.agentPlaygroundDestroy.next(true);
    this.agentPlaygroundDestroy.complete();
  }

  private generateMockItems(
    type: string,
    name: string,
    icon: string,
    count: number,
  ): ToolItem[] {
    return Array.from({ length: count }, (_, i) => ({
      id: `${type}${i + 1}`,
      name,
      description:
        'AI agents are software programs that use artificial intelligence to perform tasks and achieve goals.',
      icon,
      type: type as any,
    }));
  }

  private loadPrompts(): void {
    this.promptsService.fetchAllPrompts().subscribe({
      next: (prompts: any[]) => {
        // Filter prompts based on agent type
        const filteredPrompts = this.filterPromptsByAgentType(prompts);

        this.allToolItems['prompts'] = filteredPrompts.map((prompt: any) => ({
          id: prompt.id?.toString() || Math.random().toString(),
          name: prompt.name || prompt.title || 'Unknown Prompt',
          description:
            prompt.description ||
            prompt.descriptionConsolidated ||
            'AI prompt for agent tasks',
          icon: 'assets/images/prompt.png',
          type: 'prompt',
          promptType: prompt.type, // Store the original prompt type
          // Preserve all prompt details for collaborative agent payload
          role: prompt.role,
          goal: prompt.goal,
          backstory: prompt.backstory,
          expectedOutput: prompt.expectedOutput,
          descriptionConsolidated: prompt.descriptionConsolidated,
          expectedOutputConsolidated: prompt.expectedOutputConsolidated,
          // Store the full original prompt data
          originalPromptData: prompt,
        }));
      },
      error: (error) => {
        console.error('Error loading prompts:', error);
      },
    });
  }

  private filterPromptsByAgentType(prompts: any[]): any[] {
    // Ensure prompts is an array and not empty
    if (!Array.isArray(prompts) || prompts.length === 0) {
      return [];
    }

    let filtered: any[];

    if (this.currentAgentType === 'collaborative') {
      // For collaborative agents: only show "zero shot" prompts
      filtered = prompts.filter((prompt) => {
        const promptType = prompt.type?.trim(); // Handle potential whitespace
        return promptType === 'zero shot';
      });
    } else {
      // For individual agents: show both "free form" and "zero shot" prompts
      filtered = prompts.filter((prompt) => {
        const promptType = prompt.type?.trim(); // Handle potential whitespace
        return promptType === 'zero shot' || promptType === 'free form';
      });
    }

    return filtered;
  }

  private loadModels(): void {
    if (this.currentAgentType === 'collaborative') {
      console.log('🔗 COLLABORATIVE AGENT: Loading models from direct API...');
      console.log(
        'API URL: https://avaplus-dev.avateam.io/v1/api/admin/ava/force/model?modelType=Generative',
      );
      this.agentService.getCollaborativeModels().subscribe({
        next: (response: any) => {
          console.log('Raw models from direct API:', response);
          // Handle the new API structure - response is directly an array
          if (response && Array.isArray(response)) {
            this.allToolItems['models'] = response.map((model: any) => ({
              id: model.id,
              name: `${model.modelDeploymentName} (${model.model})`,
              description:
                model.modelDescription ||
                `${model.modelType} model via ${model.aiEngine}`,
              icon: 'assets/images/Boundingbox.png',
              type: 'model' as const,
              model: model.model,
              aiEngine: model.aiEngine,
              modelType: model.modelType,
            }));
          } else if (response && response.models) {
            // Fallback for old API structure
            this.allToolItems['models'] = response.models.map((model: any) => ({
              id: model.id,
              name: model.model,
              description: model.modelDescription,
              icon: 'assets/images/Boundingbox.png',
              type: 'model' as const,
            }));
          }
          console.log(
            'Collaborative models loaded and mapped:',
            this.allToolItems['models'],
          );
          console.log(
            'Total collaborative models loaded:',
            this.allToolItems['models'].length,
          );
        },
        error: (error: any) => {
          console.error('Error loading models from direct API:', error);
          // Fallback to labels API if direct API fails
          this.loadModelsFromLabels();
        },
      });
    } else {
      // Individual agents use labels API
      console.log('👤 INDIVIDUAL AGENT: Loading models from labels API...');
      this.loadModelsFromLabels();
    }
  }

  private loadModelsFromLabels(): void {
    console.log('Loading models from labels API...');
    this.agentService.getModelsFromLabels().subscribe({
      next: (models: any[]) => {
        console.log('Raw models from labels API:', models);
        this.allToolItems['models'] = models.map((model: any) => ({
          id: model.id,
          name: model.name,
          description: model.description,
          icon: model.icon,
          type: 'model' as const,
        }));
        console.log('Models loaded and mapped:', this.allToolItems['models']);
        console.log('Total models loaded:', this.allToolItems['models'].length);
      },
      error: (error: any) => {
        console.error('Error loading models from labels API:', error);
        // Fallback to mock data if API fails
        this.allToolItems['models'] = this.generateMockItems(
          'model',
          'Model Name',
          'assets/images/Boundingbox.png',
          2,
        );
        console.log('Using mock models:', this.allToolItems['models']);
      },
    });
  }

  // Load models from cached labels data
  private loadModelsFromCache(): void {
    console.log('Loading models from cached labels...');
    if (!this.labelsCache) return;

    const categoryLabels = this.labelsCache.categoryLabels || [];
    const modelCategory = categoryLabels.find(
      (category: any) => category.categoryId === 1,
    );

    if (modelCategory) {
      const modelLabel = modelCategory.labels.find(
        (label: any) => label.labelCode === 'MODEL',
      );
      if (modelLabel && modelLabel.labelValues) {
        const parsedModels = this.agentService.parseLabelValues(
          modelLabel.labelValues,
        );
        this.allToolItems['models'] = parsedModels.map((option) => ({
          id: option.value, // This is the actual model ID we need for payload
          name: option.name,
          type: 'model',
          icon: 'assets/images/model.png',
          description: `Model: ${option.name}`,
        }));
        console.log('Models loaded from cache:', this.allToolItems['models']);
      }
    }
  }

  private loadKnowledgeBase(): void {
    if (this.currentAgentType === 'collaborative') {
      console.log(
        '🔗 COLLABORATIVE AGENT: Loading knowledge bases from direct API...',
      );
      console.log(
        'API URL: https://avaplus-dev.avateam.io/v1/api/embedding/ava/force/knowledge',
      );
      this.agentService.getCollaborativeKnowledgeBases().subscribe({
        next: (response: any) => {
          console.log('Raw knowledge bases from direct API:', response);
          // Handle the new API structure - response is directly an array
          if (response && Array.isArray(response)) {
            this.allToolItems['knowledge'] = response
              .filter((kb: any) => kb.isactive) // Only show active knowledge bases
              .map((kb: any) => ({
                id: kb.id,
                name: kb.collectionName,
                description:
                  kb.description || `Knowledge Base: ${kb.collectionName}`,
                icon: 'assets/images/import_contacts.png',
                type: 'knowledge' as const,
                vectorDb: kb.vectorDb,
                modelRef: kb.modelRef,
                splitSize: kb.splitSize,
              }));
          }
          console.log(
            'Collaborative knowledge bases loaded and mapped:',
            this.allToolItems['knowledge'],
          );
          console.log(
            'Total collaborative knowledge bases loaded:',
            this.allToolItems['knowledge'].length,
          );
        },
        error: (error) => {
          console.error(
            'Error loading knowledge bases from direct API:',
            error,
          );
          // Fallback to labels API if direct API fails
          this.loadKnowledgeBaseFromLabels();
        },
      });
    } else {
      // Individual agents use labels API
      console.log(
        '👤 INDIVIDUAL AGENT: Loading knowledge bases from labels API...',
      );
      this.loadKnowledgeBaseFromLabels();
    }
  }

  private loadKnowledgeBaseFromLabels(): void {
    console.log('Loading knowledge bases from labels API...');
    this.agentService.getKnowledgeBasesFromLabels().subscribe({
      next: (knowledgeItems) => {
        console.log('Raw knowledge bases from labels API:', knowledgeItems);
        this.allToolItems['knowledge'] = knowledgeItems.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description,
          icon: item.icon,
          type: 'knowledge' as const,
        }));
        console.log(
          'Knowledge bases loaded and mapped:',
          this.allToolItems['knowledge'],
        );
        console.log(
          'Total knowledge bases loaded:',
          this.allToolItems['knowledge'].length,
        );
      },
      error: (error) => {
        console.error('Error loading knowledge bases from labels API:', error);
        // Fallback to mock data if API fails
        this.allToolItems['knowledge'] = this.generateMockItems(
          'knowledge',
          'Knowledge Base Name',
          'assets/images/import_contacts.png',
          2,
        );
        console.log(
          'Using mock knowledge bases:',
          this.allToolItems['knowledge'],
        );
      },
    });
  }

  // Load knowledge bases from cached labels data
  private loadKnowledgeBaseFromCache(): void {
    console.log('Loading knowledge bases from cached labels...');
    if (!this.labelsCache) return;

    const categoryLabels = this.labelsCache.categoryLabels || [];
    const iclCategory = categoryLabels.find(
      (category: any) => category.categoryId === 2,
    );

    if (iclCategory) {
      const knowledgeLabel = iclCategory.labels.find(
        (label: any) => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME',
      );
      if (knowledgeLabel && knowledgeLabel.labelValues) {
        const parsedKnowledge = this.agentService.parseLabelValues(
          knowledgeLabel.labelValues,
        );
        this.allToolItems['knowledge'] = parsedKnowledge.map((option) => ({
          id: option.value, // This is the actual knowledge base ID we need for payload
          name: option.name,
          type: 'knowledge',
          icon: 'assets/images/knowledge.png',
          description: `Knowledge Base: ${option.name}`,
        }));
        console.log(
          'Knowledge bases loaded from cache:',
          this.allToolItems['knowledge'],
        );
      }
    }
  }

  private loadTools(): void {
    console.log('Loading tools...');
    // Load both built-in and user-defined tools
    const builtInTools$ = this.toolsService.getBuiltInToolsList();
    const userTools$ = this.toolsService.getUserToolsList();

    // Load built-in tools first
    builtInTools$.subscribe({
      next: (response: any) => {
        console.log('Raw built-in tools from API:', response);
        const builtInTools = response.tools || [];
        const builtInToolItems = builtInTools.map((tool: any) => ({
          id: `builtin-${tool.toolId}`,
          name: tool.toolName || 'Unknown Built-in Tool',
          description: 'Built-in tool for AI agent tasks',
          icon: 'assets/images/build.png',
          type: 'tool',
        }));
        console.log('Built-in tools mapped:', builtInToolItems);

        // Then load user-defined tools
        userTools$.subscribe({
          next: (userResponse: any) => {
            console.log('Raw user tools from API:', userResponse);
            const userTools = userResponse.tools || [];
            const userToolItems = userTools.map((tool: any) => ({
              id: `user-${tool.toolId}`,
              name: tool.toolName || 'Unknown User Tool',
              description:
                tool.toolDescription || 'User-defined tool for AI agent tasks',
              icon: 'assets/images/build.png',
              type: 'tool',
            }));
            console.log('User tools mapped:', userToolItems);

            // Combine both built-in and user-defined tools
            this.allToolItems['tools'] = [
              ...builtInToolItems,
              ...userToolItems,
            ];
            console.log('All tools combined:', this.allToolItems['tools']);
            console.log(
              'Total tools loaded:',
              this.allToolItems['tools'].length,
            );
          },
          error: (error) => {
            console.error('Error loading user tools:', error);
            // Use only built-in tools if user tools fail
            this.allToolItems['tools'] = builtInToolItems;
            console.log(
              'Using only built-in tools:',
              this.allToolItems['tools'],
            );
          },
        });
      },
      error: (error: any) => {
        console.error('Error loading built-in tools:', error);
        // Fallback to mock data if both APIs fail
        this.allToolItems['tools'] = this.generateMockItems(
          'tool',
          'Tool Name',
          'assets/images/build.png',
          2,
        );
      },
    });
  }

  private loadGuardrails(): void {
    console.log('Loading guardrails from labels API...');
    this.agentService.getGuardrailsFromLabels().subscribe({
      next: (guardrails: any[]) => {
        console.log('Raw guardrails from labels API:', guardrails);
        this.allToolItems['guardrails'] = guardrails.map((guardrail: any) => ({
          id: guardrail.id,
          name: guardrail.name,
          description: guardrail.description,
          icon: guardrail.icon,
          type: 'guardrail' as const,
          code: guardrail.code,
        }));
        console.log(
          'Guardrails loaded and mapped:',
          this.allToolItems['guardrails'],
        );
        console.log(
          'Total guardrails loaded:',
          this.allToolItems['guardrails'].length,
        );
      },
      error: (error: any) => {
        console.error('Error loading guardrails from labels API:', error);
        // Fallback to mock data if API fails
        this.allToolItems['guardrails'] = this.generateMockItems(
          'guardrail',
          'Guardrail Name',
          'assets/images/swords.png',
          2,
        );
        console.log('Using mock guardrails:', this.allToolItems['guardrails']);
      },
    });
  }

  // Load guardrails from cached labels data
  private loadGuardrailsFromCache(): void {
    console.log('Loading guardrails from cached labels...');
    if (!this.labelsCache) return;

    const categoryLabels = this.labelsCache.categoryLabels || [];
    const otherCategory = categoryLabels.find(
      (category: any) => category.categoryId === 3,
    );

    if (otherCategory) {
      // Include ALL toggle labels from Other category, including "Enable Guardrails"
      this.allToolItems['guardrails'] = otherCategory.labels
        .filter((label: any) => label.labelType === 'Toggle') // Include all toggle types
        .map((label: any) => ({
          id: label.labelId.toString(),
          name: label.labelName,
          code: label.labelCode,
          type: 'guardrail' as const,
          icon: 'assets/images/guardrail.png',
          description: label.labelInfo || `Guardrail: ${label.labelName}`,
        }));
      console.log(
        'Guardrails loaded from cache:',
        this.allToolItems['guardrails'],
      );
      console.log(
        'Total guardrails loaded:',
        this.allToolItems['guardrails'].length,
      );
    }
  }

  get currentTabTools(): ToolItem[] {
    return this.allToolItems[this.activeTab] || [];
  }

  get filteredTools(): ToolItem[] {
    const currentTools = this.currentTabTools;

    // Filter out tools that are already added to canvas
    const availableTools = currentTools.filter((tool) => {
      // Check if this tool is already on the canvas
      const isAlreadyAdded = this.buildAgentNodes.some(
        (node) => node.originalToolData && node.originalToolData.id === tool.id,
      );
      return !isAlreadyAdded;
    });

    if (!this.searchQuery) return availableTools;

    const query = this.searchQuery.toLowerCase();
    return availableTools.filter(
      (tool) =>
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query),
    );
  }

  onTabChange(tabValue: string | number): void {
    this.activeTab = tabValue.toString();
    this.searchQuery = ''; // Clear search when changing tabs
    this.searchForm.get('search')?.setValue('', { emitEvent: false }); // Clear search form
  }

  onSearchChange(query: string): void {
    this.searchQuery = query;
  }

  onCustomTabChange(tabValue: string | number): void {
    this.activeTab = tabValue.toString();
    this.searchQuery = ''; // Clear search when changing tabs
    this.searchForm.get('search')?.setValue('', { emitEvent: false }); // Clear search form
  }

  // Sidebar toggle functionality
  toggleSidebar(): void {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  isActiveCustomTab(tabValue: string | number): boolean {
    return this.activeTab === tabValue.toString();
  }

  isTabDisabled(tabValue: string | number): boolean {
    // Add logic here if you want to disable certain tabs
    return false;
  }

  getTabIcon(tab: any): string {
    // Map tab values to Lucide icon names
    const iconMap: { [key: string]: string } = {
      prompts: 'FileText',
      models: 'Box',
      knowledge: 'BookOpen',
      guardrails: 'Swords',
      tools: 'Wrench',
    };
    return iconMap[tab.value] || 'Circle';
  }

  // Handle automatic tab switching based on node type and requirements
  private handleAutoTabSwitch(nodeType: string): void {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];

    // Check if the current node type has a limit of 1 (mandatory single item)
    const currentNodeLimit =
      currentLimits[nodeType as keyof typeof currentLimits];

    if (currentNodeLimit === 1) {
      // This is a mandatory single item, switch to next tab
      const nextTab = this.getNextRequiredTab(nodeType);
      if (nextTab) {
        console.log(
          `Auto-switching from ${this.activeTab} to ${nextTab} after adding ${nodeType}`,
        );
        this.activeTab = nextTab;
        this.searchQuery = ''; // Clear search when auto-switching tabs
        this.searchForm.get('search')?.setValue('', { emitEvent: false }); // Clear search form
      }
    }
    // For unlimited items (knowledge, tools, guardrails), stay on current tab
  }

  // Get the next required tab based on current node type
  private getNextRequiredTab(currentNodeType: string): string | null {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];

    // Define the progression order for mandatory items
    if (currentNodeType === 'prompt') {
      // After adding prompt, go to models (mandatory)
      const hasModel = this.buildAgentNodes.some(
        (node) => node.type === 'model',
      );
      if (!hasModel && currentLimits['model'] === 1) {
        return 'models';
      }
    } else if (currentNodeType === 'model') {
      // After adding model, go to knowledge base (can add multiple)
      return 'knowledge';
    }

    return null; // No automatic switch needed
  }

  // Preview functionality methods
  onItemPreview(item: ToolItem): void {
    this.selectedItem = item;
    this.showPreview = true;
    this.loadPreviewData(item);
  }

  closePreview(): void {
    this.showPreview = false;
    this.selectedItem = null;
    this.previewData = null;
  }

  private loadPreviewData(item: ToolItem): void {
    this.isLoadingPreview = true;
    this.previewData = null;

    // Load different data based on item type
    switch (item.type) {
      case 'model':
        this.loadModelPreview(item.id);
        break;
      case 'prompt':
        this.loadPromptPreview(item.id);
        break;
      case 'tool':
        this.loadToolPreview(item.id);
        break;
      case 'knowledge':
        this.loadKnowledgePreview(item.id);
        break;
      case 'guardrail':
        this.loadGuardrailPreview(item.id);
        break;
      default:
        this.isLoadingPreview = false;
        this.previewData = {
          error: 'Preview not available for this item type',
        };
    }
  }

  private loadModelPreview(modelId: string): void {
    // Use the model data from labels API instead of separate service
    const modelData = this.allToolItems['models'].find((m) => m.id === modelId);
    if (modelData) {
      this.previewData = {
        type: 'model',
        title: modelData.name || 'Model Details',
        data: modelData,
      };
    } else {
      this.previewData = { error: 'Model details not found' };
    }
    this.isLoadingPreview = false;
  }

  private loadPromptPreview(promptId: string): void {
    this.promptsService.getPromptById(promptId).subscribe({
      next: (prompt) => {
        if (prompt) {
          this.previewData = {
            type: 'prompt',
            title: 'Prompt Details',
            data: {
              id: prompt.id,
              name: prompt.name,
              role: prompt.role,
              goal: prompt.goal,
              description: prompt.description,
              backstory: prompt.backstory,
              expectedOutput: prompt.expectedOutput,
              categoryName: prompt.categoryName,
              domainName: prompt.domainName,
              type: prompt.type,
              updatedAt: prompt.updatedAt,
            },
          };
        } else {
          this.previewData = { error: 'Prompt not found' };
        }
        this.isLoadingPreview = false;
      },
      error: (error) => {
        console.error('Error loading prompt preview:', error);
        this.previewData = { error: 'Failed to load prompt details' };
        this.isLoadingPreview = false;
      },
    });
  }

  private loadToolPreview(toolId: string): void {
    // Extract numeric ID from string IDs like "builtin-85" or "user-85"
    let numericToolId: number;

    if (toolId.startsWith('builtin-') || toolId.startsWith('user-')) {
      const idPart = toolId.split('-')[1];
      numericToolId = Number(idPart);
    } else {
      numericToolId = Number(toolId);
    }

    if (isNaN(numericToolId)) {
      console.error('Invalid tool ID:', toolId);
      this.previewData = { error: 'Invalid tool ID provided' };
      this.isLoadingPreview = false;
      return;
    }

    // Use the getUserToolDetails API to get specific tool details
    this.toolsService.getUserToolDetails(numericToolId).subscribe({
      next: (response: any) => {
        const tool =
          response.tools && response.tools[0] ? response.tools[0] : response;
        if (tool) {
          this.previewData = {
            type: 'tool',
            title: 'Tool Details',
            data: {
              id: tool.toolId || toolId,
              name: tool.toolName || 'Tool Name',
              description: tool.toolDescription || 'No description available',
              className: tool.toolClassName || 'N/A',
              functionality:
                tool.toolClassDef || 'No functionality details available',
              createdBy: tool.createdBy || 'System',
              createdOn:
                tool.createTimestamp ||
                tool.updateTimestamp ||
                new Date().toISOString(),
              isApproved: tool.isApproved || false,
            },
          };
        } else {
          this.previewData = { error: 'Tool not found' };
        }
        this.isLoadingPreview = false;
      },
      error: (error) => {
        console.error('Error loading tool preview:', error);
        this.previewData = { error: 'Failed to load tool details' };
        this.isLoadingPreview = false;
      },
    });
  }

  private loadKnowledgePreview(knowledgeId: string): void {
    // Use the knowledge base data from labels API instead of separate service
    const knowledgeData = this.allToolItems['knowledge'].find(
      (k) => k.id === knowledgeId,
    );
    if (knowledgeData) {
      this.previewData = {
        type: 'knowledge',
        title: knowledgeData.name || 'Knowledge Base',
        data: {
          name: knowledgeData.name,
          description: knowledgeData.description,
          id: knowledgeData.id,
        },
      };
    } else {
      this.previewData = { error: 'Knowledge base details not found' };
    }
    this.isLoadingPreview = false;
  }

  private loadGuardrailPreview(guardrailId: string): void {
    // Extract numeric ID from string IDs like "guardrail-420"
    let targetLabelId: string;

    if (guardrailId.startsWith('guardrail-')) {
      targetLabelId = guardrailId.split('-')[1];
    } else {
      targetLabelId = guardrailId;
    }

    // Use the existing getLabels API to get all guardrails and find the specific one
    this.agentService.getLabels().subscribe({
      next: (response: any) => {
        const categoryLabels = response.categoryLabels || [];
        // Find the "Other" category (categoryId: 3) which contains guardrails
        const otherCategory = categoryLabels.find(
          (category: any) => category.categoryId === 3,
        );

        if (otherCategory && otherCategory.labels) {
          // Find the specific guardrail by labelId
          const guardrail = otherCategory.labels.find(
            (label: any) => label.labelId.toString() === targetLabelId,
          );

          if (guardrail) {
            this.previewData = {
              type: 'guardrail',
              title: 'Guardrail Details',
              data: {
                id: guardrail.labelId,
                name: guardrail.labelName,
                description: guardrail.labelInfo || 'No description available',
                labelCode: guardrail.labelCode,
                categoryId: otherCategory.categoryId,
                categoryName: otherCategory.categoryName,
                // Add any other fields that are available in the label object
                ...guardrail, // Spread all available fields
              },
            };
          } else {
            this.previewData = { error: 'Guardrail not found' };
          }
        } else {
          this.previewData = { error: 'Guardrails category not found' };
        }
        this.isLoadingPreview = false;
      },
      error: (error: any) => {
        console.error('Error loading guardrail preview:', error);
        this.previewData = { error: 'Failed to load guardrail details' };
        this.isLoadingPreview = false;
      },
    });
  }

  getIconForType(type: string): string {
    const iconMap: { [key: string]: string } = {
      prompt: 'FileText',
      model: 'assets/images/deployed_code.png',
      knowledge: 'assets/images/import_contacts.png',
      tool: 'assets/images/build.png',
      guardrail: 'assets/images/swords.png',
    };
    return iconMap[type] || 'assets/images/build.png'; // Default to tool icon
  }

  // Helper method to get additional fields for guardrail preview
  getAdditionalFields(data: any): { key: string; value: any }[] {
    if (!data) return [];

    // Define the fields we already show explicitly
    const excludeFields = [
      'id',
      'name',
      'description',
      'labelCode',
      'categoryName',
      'categoryId',
      'labelInfo',
    ];

    // Get all other fields
    const additionalFields: { key: string; value: any }[] = [];

    Object.keys(data).forEach((key) => {
      if (
        !excludeFields.includes(key) &&
        data[key] !== null &&
        data[key] !== undefined
      ) {
        additionalFields.push({
          key: this.formatFieldName(key),
          value: data[key],
        });
      }
    });

    return additionalFields;
  }

  // Helper method to format field names for display
  private formatFieldName(fieldName: string): string {
    return fieldName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
      .trim();
  }

  // Configure tabs based on agent type
  private configureTabsForAgentType(): void {
    if (this.currentAgentType === 'individual') {
      // Individual: prompts (mandatory), models (mandatory), knowledge (multiple), guardrails (multiple)
      this.tabs = [
        { label: 'Prompts', value: 'prompts', icon: 'FileText' },
        { label: 'Models', value: 'models', icon: 'Box' },
        { label: 'Knowledge Base', value: 'knowledge', icon: 'BookOpen' },
        { label: 'Guardrails', value: 'guardrails', icon: 'Swords' },
      ];
    } else {
      // Collaborative: prompts (mandatory), models (mandatory), knowledge (multiple), tools (multiple)
      this.tabs = [
        { label: 'Prompts', value: 'prompts', icon: 'FileText' },
        { label: 'Models', value: 'models', icon: 'Box' },
        { label: 'Knowledge Base', value: 'knowledge', icon: 'BookOpen' },
        { label: 'Tools', value: 'tools', icon: 'Wrench' },
      ];
    }

    // Set default active tab to models (highlighted)
    this.activeTab = 'models';
  }

  // Check if we can add a node of the given type
  private canAddNodeOfType(nodeType: string): boolean {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    const limit = currentLimits[nodeType as keyof typeof currentLimits];

    if (limit === undefined || limit === -1) {
      return true; // Unlimited
    }

    const currentCount = this.buildAgentNodes.filter(
      (node) => node.type === nodeType,
    ).length;
    return currentCount < limit;
  }

  // Get the limit for a node type
  private getNodeLimit(nodeType: string): number {
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    return currentLimits[nodeType as keyof typeof currentLimits] || -1;
  }

  // Remove item from the list when it's added to canvas
  private removeItemFromList(tool: any): void {
    // Map node types to the correct allToolItems keys
    let tabType: string;
    if (tool.type === 'guardrail') {
      tabType = 'guardrails';
    } else if (tool.type === 'knowledge') {
      tabType = 'knowledge'; // Keep as 'knowledge', not 'knowledges'
    } else {
      tabType = `${tool.type}s`; // For prompts, models, tools
    }
    if (this.allToolItems[tabType]) {
      this.allToolItems[tabType] = this.allToolItems[tabType].filter(
        (item) => item.id !== tool.id,
      );
      console.log(`Removed ${tool.name} from ${tabType} list`);
    }
  }

  // Add item back to list when it's removed from canvas
  private addItemBackToList(node: BuildAgentNodeData): void {
    // Map node types to the correct allToolItems keys
    let tabType: string;
    if (node.type === 'guardrail') {
      tabType = 'guardrails';
    } else if (node.type === 'knowledge') {
      tabType = 'knowledge'; // Keep as 'knowledge', not 'knowledges'
    } else {
      tabType = `${node.type}s`; // For prompts, models, tools
    }
    if (this.allToolItems[tabType]) {
      // Check if item already exists in the list to avoid duplicates
      // Use original tool ID if available, otherwise fall back to node ID and name
      const originalToolId = node.originalToolData?.id;
      const existingItem = this.allToolItems[tabType].find(
        (item) =>
          item.name === node.name ||
          item.id === node.id ||
          (originalToolId && item.id === originalToolId),
      );

      if (!existingItem) {
        // Use original tool data if available, otherwise create a new item
        let toolItem: ToolItem;

        if (node.originalToolData) {
          // Restore the original tool data with all its properties
          toolItem = { ...node.originalToolData };
        } else {
          // Fallback to creating a new item (for backward compatibility)
          toolItem = {
            id: node.id,
            name: node.name,
            description: `${node.type} component: ${node.name}`,
            icon: node.icon || this.getIconForType(node.type),
            type: node.type,
          };
        }

        this.allToolItems[tabType].push(toolItem);
        console.log(`Added ${node.name} back to ${tabType} list`);
      }
    }
  }

  // Check if a tab is required
  isTabRequired(tabValue: string): boolean {
    const nodeType =
      tabValue === 'guardrails' ? 'guardrail' : tabValue.slice(0, -1); // Remove 's' from plural
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];
    const limit = currentLimits[nodeType as keyof typeof currentLimits];
    return limit === 1; // Required if limit is exactly 1
  }

  // Validate that all mandatory components are present
  validateMandatoryComponents(): {
    isValid: boolean;
    missingComponents: string[];
  } {
    const missingComponents: string[] = [];
    const currentLimits =
      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];

    Object.entries(currentLimits).forEach(([nodeType, limit]) => {
      if (limit === 1) {
        // Mandatory component
        const hasComponent = this.buildAgentNodes.some(
          (node) => node.type === nodeType,
        );
        if (!hasComponent) {
          missingComponents.push(nodeType);
        }
      }
    });

    return {
      isValid: missingComponents.length === 0,
      missingComponents,
    };
  }

  // Load data based on agent type
  private loadDataForAgentType(): void {
    // Clear existing data to avoid stale data
    this.allToolItems = {
      prompts: [],
      models: [],
      knowledge: [],
      tools: [],
      guardrails: [],
    };

    // Load labels API once and cache it, then load all data from cache
    this.loadLabelsAndData();
  }

  // Load labels API once and use cached data for all subsequent calls
  private loadLabelsAndData(): void {
    console.log('Loading labels API once...');
    this.agentService.getLabels().subscribe({
      next: (response: any) => {
        console.log('Labels API response:', response);
        this.labelsCache = response;

        // Load prompts for all agent types
        this.loadPrompts();

        // Load data based on agent type
        if (this.currentAgentType === 'individual') {
          // Individual agents use labels API for everything
          console.log('Loading individual agent data from labels API...');
          this.loadModelsFromCache();
          this.loadKnowledgeBaseFromCache();
          this.loadGuardrailsFromCache();
        } else {
          // Collaborative agents use direct APIs for models/knowledge, tools for others
          console.log('Loading collaborative agent data from direct APIs...');
          this.loadModels(); // Uses direct API for collaborative
          this.loadKnowledgeBase(); // Uses direct API for collaborative
          this.loadTools();
        }
      },
      error: (error) => {
        console.error('Error loading labels API:', error);
        // Fallback to individual API calls if labels API fails
        this.loadPrompts();
        this.loadModels();
        this.loadKnowledgeBase();
        if (this.currentAgentType === 'individual') {
          this.loadGuardrails();
        } else {
          this.loadTools();
        }
      },
    });
  }

  // Get dynamic title for the builder
  getBuilderTitle(): string {
    return this.currentAgentType === 'individual'
      ? 'Individual Agent Builder'
      : 'Collaborative Agent Builder';
  }

  onDragStart(event: DragEvent, tool: ToolItem): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/reactflow', JSON.stringify(tool));
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onCanvasDropped(event: {
    event: DragEvent;
    position: { x: number; y: number };
  }): void {
    const toolData = event.event.dataTransfer?.getData('application/reactflow');
    if (!toolData) return;

    try {
      const tool = JSON.parse(toolData);

      // Check if we can add this node type based on limits
      if (!this.canAddNodeOfType(tool.type)) {
        const limit = this.getNodeLimit(tool.type);

        // For limited node types (like model and prompt), replace the existing node
        if (limit === 1) {
          const existingNode = this.buildAgentNodes.find(
            (node) => node.type === tool.type,
          );
          if (existingNode) {
            console.log(
              `Replacing existing ${tool.type} node:`,
              existingNode.name,
              'with:',
              tool.name,
            );
            // Remove the existing node
            this.onDeleteNode(existingNode.id);
            // Continue with adding the new node
          }
        } else {
          this.showErrorMessage(
            'Node Limit Reached',
            `You can only add ${limit} ${tool.type} node(s) for ${this.currentAgentType} agents.`,
          );
          return;
        }
      }

      const autoPosition = this.calculateAutoPosition(tool.type);

      const buildAgentNode: BuildAgentNodeData = {
        id: this.generateNodeId(),
        name: tool.name,
        icon: tool.type === 'prompt' ? tool.icon : undefined, // Only use icon for prompts
        type: tool.type,
        position: autoPosition,
        originalToolData: tool, // Store the original tool data including ID
      };

      const nodeWidth = this.isExecuteMode ? 55 : 90; // Execute mode nodes are 55px (circular with border)
      const newCanvasNode: CanvasNode = {
        id: buildAgentNode.id,
        type: 'build-agent',
        data: { ...buildAgentNode, width: nodeWidth },
        position: autoPosition,
      };

      // Add the new node and recalculate positions to maintain hierarchy
      this.buildAgentNodes = [...this.buildAgentNodes, buildAgentNode];
      this.canvasNodes = [...this.canvasNodes, newCanvasNode];

      // Recalculate positions to maintain hierarchy order
      this.recalculateNodePositionsAfterAddition();

      // Wait for DOM to update, then create connections and update connection points
      setTimeout(() => {
        this.createAutomaticConnections(buildAgentNode);
        // Force update of connection points after nodes are positioned
        if (this.canvasBoardComponent) {
          this.canvasBoardComponent.updateNodeConnectionPoints();
        }
      }, 150);

      // Always remove item from the list when added to canvas (for all types)
      this.removeItemFromList(tool);

      // Trigger change detection to update filtered tools
      this.cdr.detectChanges();

      // Auto-switch to next tab based on node type and current agent requirements
      this.handleAutoTabSwitch(tool.type);
    } catch (error) {
      console.error('Error adding node:', error);
    }
  }

  onNodeSelected(nodeId: string): void {
    console.log('Node selected:', nodeId);
    this.selectedNodeId = nodeId;
    // Single click only selects the node, no preview
  }

  onNodeDoubleClicked(nodeId: string): void {
    // Find the node data and trigger preview on double-click
    const selectedNode = this.buildAgentNodes.find(
      (node) => node.id === nodeId,
    );
    if (selectedNode) {
      // Find the corresponding tool item to show preview
      const toolItem = this.findToolItemForNode(selectedNode);
      if (toolItem) {
        this.onItemPreview(toolItem);
      } else {
        // For nodes that don't have corresponding tool items, create a basic tool item
        // but use the actual data from allToolItems if available
        let actualToolItem: ToolItem | null = null;

        // Try to find the actual tool item by name and type
        if (this.allToolItems[selectedNode.type + 's']) {
          actualToolItem =
            this.allToolItems[selectedNode.type + 's'].find(
              (item: any) => item.name === selectedNode.name,
            ) || null;
        }

        if (actualToolItem) {
          this.onItemPreview(actualToolItem);
        } else {
          // Create a basic tool item for preview if not found in allToolItems
          const basicToolItem: ToolItem = {
            id: selectedNode.id,
            name: selectedNode.name,
            type: selectedNode.type,
            description: `${selectedNode.type} component: ${selectedNode.name}`,
            icon: selectedNode.icon || this.getIconForType(selectedNode.type),
          };
          this.onItemPreview(basicToolItem);
        }
      }
    }
  }

  // Find the corresponding tool item for a node to show preview
  private findToolItemForNode(node: BuildAgentNodeData): ToolItem | null {
    const nodeType = node.type;
    const nodeName = node.name;

    // Search in the appropriate tool items array based on node type
    switch (nodeType) {
      case 'prompt':
        return (
          this.allToolItems['prompts']?.find(
            (item: any) => item.name === nodeName,
          ) || null
        );
      case 'model':
        return (
          this.allToolItems['models']?.find(
            (item: any) => item.name === nodeName,
          ) || null
        );
      case 'knowledge':
        return (
          this.allToolItems['knowledge']?.find(
            (item: any) => item.name === nodeName,
          ) || null
        );
      case 'tool':
        return (
          this.allToolItems['tools']?.find(
            (item: any) => item.name === nodeName,
          ) || null
        );
      case 'guardrail':
        return (
          this.allToolItems['guardrails']?.find(
            (item: any) => item.name === nodeName,
          ) || null
        );
      default:
        return null;
    }
  }

  onNodeMoved(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    // Update build agent nodes with new array reference
    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (buildAgentNodeIndex !== -1) {
      this.buildAgentNodes = [
        ...this.buildAgentNodes.slice(0, buildAgentNodeIndex),
        {
          ...this.buildAgentNodes[buildAgentNodeIndex],
          position: event.position,
        },
        ...this.buildAgentNodes.slice(buildAgentNodeIndex + 1),
      ];
    }

    // Update canvas nodes with new array reference
    const canvasNodeIndex = this.canvasNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (canvasNodeIndex !== -1) {
      this.canvasNodes = [
        ...this.canvasNodes.slice(0, canvasNodeIndex),
        { ...this.canvasNodes[canvasNodeIndex], position: event.position },
        ...this.canvasNodes.slice(canvasNodeIndex + 1),
      ];
    }

    // Force change detection cycle to ensure connections update immediately
    this.cdr.detectChanges();
  }

  onDeleteNode(nodeId: string): void {
    // Find the node being deleted to add it back to the list
    const nodeToDelete = this.buildAgentNodes.find(
      (node) => node.id === nodeId,
    );

    if (nodeToDelete) {
      // Always add the item back to the list when deleted (for all types)
      this.addItemBackToList(nodeToDelete);
      console.log(
        `Added ${nodeToDelete.name} back to ${nodeToDelete.type} list`,
      );
    }

    // Remove the node from all arrays
    this.buildAgentNodes = this.buildAgentNodes.filter(
      (node) => node.id !== nodeId,
    );
    this.canvasNodes = this.canvasNodes.filter((node) => node.id !== nodeId);
    this.canvasEdges = this.canvasEdges.filter(
      (edge) => edge.source !== nodeId && edge.target !== nodeId,
    );

    // Recalculate positions for remaining nodes to maintain hierarchy
    this.recalculateNodePositionsAfterDeletion();

    // Recreate connections based on hierarchy
    this.createAgentFlowConnections(this.buildAgentNodes);

    // Trigger change detection to update filtered tools (make deleted node available again)
    this.cdr.detectChanges();
  }

  // Recalculate node positions after deletion to maintain hierarchy
  private recalculateNodePositionsAfterDeletion(): void {
    // Group nodes by type
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      guardrail: [],
      tool: [],
    };

    this.buildAgentNodes.forEach((node) => {
      if (nodesByType[node.type]) {
        nodesByType[node.type].push(node);
      }
    });

    // Reassemble nodes in hierarchy order with recalculated positions
    const reorderedNodes: BuildAgentNodeData[] = [];
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Temporarily clear buildAgentNodes for position calculation
    this.buildAgentNodes = [];

    flowOrder.forEach((nodeType) => {
      nodesByType[nodeType].forEach((node) => {
        // Recalculate position based on current hierarchy
        node.position = this.calculateHierarchyBasedPosition(nodeType);
        reorderedNodes.push(node);
        // Add to buildAgentNodes for next position calculation
        this.buildAgentNodes.push(node);
      });
    });

    // Update arrays with reordered nodes
    this.buildAgentNodes = reorderedNodes;
    this.canvasNodes = reorderedNodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: this.isExecuteMode ? 55 : 90 },
      position: node.position,
    }));
  }

  // Recalculate node positions after addition to maintain hierarchy
  private recalculateNodePositionsAfterAddition(): void {
    // Use the same logic as deletion but for addition
    this.recalculateNodePositionsAfterDeletion();
  }

  private updateNodePosition(
    nodes: any[],
    nodeId: string,
    position: { x: number; y: number },
  ): void {
    const nodeIndex = nodes.findIndex((node) => node.id === nodeId);
    if (nodeIndex !== -1) {
      // Create a new array to trigger change detection (like workflow editor approach)
      nodes[nodeIndex] = {
        ...nodes[nodeIndex],
        position: position,
      };
    }
  }

  onConnectionCreated(edge: CanvasEdge): void {
    // Create a proper edge with unique ID
    const newEdge: CanvasEdge = {
      id:
        edge.id ||
        `edge_${edge.source}_${edge.target}_${Math.floor(Math.random() * 1000)}`,
      source: edge.source,
      target: edge.target,
      animated: edge.animated || true,
    };

    this.canvasEdges = [...this.canvasEdges, newEdge];
  }

  onStartConnection(_event: {
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }): void {
    // Canvas board handles connection logic
    // This method is called when a connection starts but the canvas board manages the temp connection
  }

  // Handle node position changes for connection point updates
  onNodePositionChanged(event: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    // Update build agent nodes with new array reference
    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (buildAgentNodeIndex !== -1) {
      this.buildAgentNodes = [
        ...this.buildAgentNodes.slice(0, buildAgentNodeIndex),
        {
          ...this.buildAgentNodes[buildAgentNodeIndex],
          position: event.position,
        },
        ...this.buildAgentNodes.slice(buildAgentNodeIndex + 1),
      ];
    }

    // Update canvas nodes with new array reference
    const canvasNodeIndex = this.canvasNodes.findIndex(
      (node) => node.id === event.nodeId,
    );
    if (canvasNodeIndex !== -1) {
      this.canvasNodes = [
        ...this.canvasNodes.slice(0, canvasNodeIndex),
        { ...this.canvasNodes[canvasNodeIndex], position: event.position },
        ...this.canvasNodes.slice(canvasNodeIndex + 1),
      ];
    }

    // Force change detection to ensure connections update immediately
    this.cdr.detectChanges();
  }

  // Toolbar actions
  onUndo(): void {
    // Implement undo functionality
  }

  onRedo(): void {
    // Implement redo functionality
  }

  onReset(): void {
    // Clear all nodes and edges
    this.buildAgentNodes = [];
    this.canvasNodes = [];
    this.canvasEdges = [];
  }

  onRun(): void {
    // Implement run/execute functionality
    this.onExecute();
  }

  // Get metadata from navbar cookies
  private getMetadataFromNavbar(): {
    orgPath: string;
    levelId: number;
    orgId?: number;
    domainId?: number;
    projectId?: number;
    teamId?: number;
    org?: string;
    domain?: string;
    project?: string;
    team?: string;
  } {
    const orgPath = this.tokenStorage.getCookie('org_path');
    let organizationPath = '';
    let levelId = 150; // Default level ID
    let orgId, domainId, projectId, teamId;
    let org, domain, project, team;

    if (orgPath) {
      const parts = orgPath.split('::');
      const usecasePath = parts[0] || '';
      const usecaseIdPath = parts[1] || '';

      // Use the names path for organizationPath
      organizationPath = usecasePath;

      // Parse the names to get individual organizational levels
      const names = usecasePath.split('@');
      if (names.length >= 4) {
        org = names[0] || '';
        domain = names[1] || '';
        project = names[2] || '';
        team = names[3] || '';
      }

      // Parse the IDs to get individual level IDs
      const ids = usecaseIdPath.split('@').map(Number);
      if (ids.length >= 4) {
        orgId = ids[0] || undefined;
        domainId = ids[1] || undefined;
        projectId = ids[2] || undefined;
        teamId = ids[3] || undefined;
        // Use team ID as the main levelId for collaborative agents
        levelId = teamId || ids[ids.length - 1] || 150;
      } else if (ids.length > 0 && ids[0]) {
        levelId = ids[0];
      }
    }

    return {
      orgPath: organizationPath,
      levelId,
      orgId,
      domainId,
      projectId,
      teamId,
      org,
      domain,
      project,
      team,
    };
  }

  // Get user signature from cookies
  private getUserSignature(): string {
    return this.tokenStorage.getDaUsername() || '<EMAIL>';
  }

  // Build configuration arrays from canvas nodes following digital ascender pattern
  private buildConfigurationArrays(): any[] {
    // Get the labels data to build complete configuration structure
    return this.buildConfigurationFromLabels();
  }

  // Build configuration for all categories, regardless of what's on the canvas
  private buildConfigurationFromLabels(): any[] {
    const configurations: any[] = [];

    // Check which nodes exist on the canvas (for determining actual values)
    const hasModel = this.buildAgentNodes.some((node) => node.type === 'model');
    const hasKnowledge = this.buildAgentNodes.some(
      (node) => node.type === 'knowledge',
    );
    const hasGuardrails = this.buildAgentNodes.some(
      (node) => node.type === 'guardrail',
    );

    console.log('=== CONFIGURATION BUILD DEBUG ===');
    console.log('Canvas nodes check:', {
      hasModel,
      hasKnowledge,
      hasGuardrails,
    });
    console.log('Current canvas nodes:', this.buildAgentNodes);
    console.log('Current mode:', this.currentMode);
    console.log('Current agent ID:', this.currentAgentId);

    // For update operations, we need to include all categories with proper values
    // const isUpdateOperation = this.currentMode === 'edit' && this.currentAgentId;

    // Model category - always include, set actual values when nodes exist
    configurations.push({
      categoryName: 'Model',
      categoryId: 1,
      configs: [
        {
          configurationName: 'MODEL',
          configurationValue: hasModel ? this.getModelConfigValue() : '',
        },
        { configurationName: 'MAX_TOKEN', configurationValue: '' },
        { configurationName: 'TEMPERATURE', configurationValue: '' },
        { configurationName: 'TOP_P', configurationValue: '' },
        { configurationName: 'FREQUENCY_PENALTY', configurationValue: '' },
        { configurationName: 'PROMPT_PREFIX', configurationValue: '' },
        { configurationName: 'PROMPT_WRAPPER', configurationValue: '' },
      ],
    });

    // ICL category - always include, set actual values when nodes exist
    configurations.push({
      categoryName: 'In Context Learning (ICL)',
      categoryId: 2,
      configs: [
        {
          configurationName: 'RAG',
          configurationValue: hasKnowledge ? 'true' : '',
        },
        { configurationName: 'RAG_MODE', configurationValue: '' },
        {
          configurationName: 'RAG_KNOWLEDGEBASE_NAME',
          configurationValue: hasKnowledge
            ? this.getKnowledgeBaseConfigValue()
            : '',
        },
        {
          configurationName: 'RAG_KNOWLEDGEBASE_MAX_RECORD_COUNT',
          configurationValue: '',
        },
        { configurationName: 'TOKEN_COMPRESSION', configurationValue: '' },
      ],
    });

    // Other category - always include with all guardrails
    const availableGuardrails = this.allToolItems['guardrails'] || [];
    if (availableGuardrails.length > 0) {
      configurations.push({
        categoryName: 'Other',
        categoryId: 3,
        configs: this.getGuardrailConfigs(), // This now includes all guardrails with proper values
      });
    }

    console.log('Final configuration payload:', configurations);
    return configurations;
  }

  // Extract numeric ID from knowledge node ID (e.g., "knowledge-456" -> "456") - kept for fallback
  private extractKnowledgeId(nodeId: string): string {
    const match = nodeId.match(/knowledge-(\d+)/);
    return match ? match[1] : nodeId;
  }

  // Extract numeric ID from prompt node ID (e.g., "prompt-789" -> "789") - kept for fallback
  private extractPromptId(nodeId: string): string {
    const match = nodeId.match(/prompt-(\d+)/);
    return match ? match[1] : nodeId;
  }

  // Get model configuration value from canvas nodes
  private getModelConfigValue(): string {
    console.log('=== MODEL CONFIG DEBUG ===');
    const modelNode = this.buildAgentNodes.find(
      (node) => node.type === 'model',
    );
    console.log('Model node found:', modelNode);

    if (modelNode) {
      console.log('Model node name:', modelNode.name);
      console.log('Model node originalToolData:', modelNode.originalToolData);

      // First try to use the original tool data if available
      if (modelNode.originalToolData && modelNode.originalToolData.id) {
        console.log(
          `Using original tool data ID: ${modelNode.originalToolData.id}`,
        );
        return modelNode.originalToolData.id.toString();
      }

      // Find the model data from the labels API using the node name
      const modelData = this.allToolItems['models'].find(
        (model) => model.name === modelNode.name,
      );

      if (modelData) {
        // Return the actual model ID from labels API (e.g., "30", "32", etc.)
        console.log(`Found model: ${modelData.name} with ID: ${modelData.id}`);
        return modelData.id.toString(); // Ensure it's a string
      }

      // Also try to find by ID if name doesn't match
      const modelDataById = this.allToolItems['models'].find(
        (model) =>
          model.id === modelNode.name || model.id.toString() === modelNode.name,
      );

      if (modelDataById) {
        console.log(
          `Found model by ID: ${modelDataById.name} with ID: ${modelDataById.id}`,
        );
        return modelDataById.id.toString();
      }

      console.warn(`Model not found for node: ${modelNode.name}`);
      console.log('Available models:', this.allToolItems['models']);
    } else {
      console.log('No model node found on canvas');
    }
    return '32'; // Default model ID
  }

  // Get knowledge base configuration value from canvas nodes
  private getKnowledgeBaseConfigValue(): string {
    console.log('=== KNOWLEDGE BASE CONFIG DEBUG ===');
    const knowledgeNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'knowledge',
    );
    console.log('Knowledge nodes found:', knowledgeNodes.length);
    console.log('Knowledge nodes:', knowledgeNodes);

    if (knowledgeNodes.length > 0) {
      // Return comma-separated list of knowledge base IDs from labels API
      const knowledgeIds = knowledgeNodes
        .map((node) => {
          console.log('Processing knowledge node:', node.name);
          console.log('Node originalToolData:', node.originalToolData);

          // First try to use the original tool data if available
          if (node.originalToolData && node.originalToolData.id) {
            console.log(
              `Using original tool data ID: ${node.originalToolData.id}`,
            );
            return node.originalToolData.id.toString();
          }

          const knowledgeData = this.allToolItems['knowledge'].find(
            (kb) => kb.name === node.name,
          );

          if (knowledgeData) {
            // Return the actual knowledge base ID from labels API (e.g., "189", "193", etc.)
            console.log(
              `Found knowledge base: ${knowledgeData.name} with ID: ${knowledgeData.id}`,
            );
            return knowledgeData.id;
          }

          console.warn(`Knowledge base not found for node: ${node.name}`);
          console.log(
            'Available knowledge bases:',
            this.allToolItems['knowledge'],
          );
          // Don't return node ID, return null and filter it out
          return null;
        })
        .filter((id) => id !== null); // Filter out null values

      console.log('Final knowledge base IDs:', knowledgeIds);
      return knowledgeIds.join(',');
    }
    return '';
  }

  // Extract numeric ID from guardrail node ID (e.g., "guardrail-770" -> "770")
  private extractGuardrailId(nodeId: string): string {
    const match = nodeId.match(/guardrail-(\d+)/);
    return match ? match[1] : nodeId;
  }

  // Get guardrail configurations from canvas nodes
  private getGuardrailConfigs(): any[] {
    const guardrailNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'guardrail',
    );
    console.log('=== GUARDRAIL CONFIG DEBUG ===');
    console.log('Guardrail nodes found:', guardrailNodes.length);
    console.log('Guardrail nodes:', guardrailNodes);

    const configs: any[] = [];

    // Always add ENABLE_GUARDRAILS with appropriate value
    configs.push({
      configurationName: 'ENABLE_GUARDRAILS',
      configurationValue: guardrailNodes.length > 0 ? 'true' : 'false',
    });

    // Get all available guardrails from the service data
    const availableGuardrails = this.allToolItems['guardrails'] || [];

    // Create a set of selected guardrail names for quick lookup
    const selectedGuardrailNames = new Set<string>();

    // First, collect all selected guardrail names
    guardrailNodes.forEach((node) => {
      // Extract the numeric ID from the node ID
      const guardrailId = this.extractGuardrailId(node.id);

      // Find the actual guardrail data by ID or name
      const guardrailData = availableGuardrails.find(
        (gr) =>
          gr.id === guardrailId || gr.id === node.id || gr.name === node.name,
      );

      if (guardrailData) {
        // Use the actual guardrail configuration name from the data
        const configName = `GUARDRAIL_${guardrailData.name
          .toUpperCase()
          .replace(/\s+/g, '_')
          .replace(/[^A-Z0-9_]/g, '')}`;
        selectedGuardrailNames.add(configName);
      } else {
        // Fallback to node name if data not found
        const configName = `GUARDRAIL_${node.name
          .toUpperCase()
          .replace(/\s+/g, '_')
          .replace(/[^A-Z0-9_]/g, '')}`;
        selectedGuardrailNames.add(configName);
      }
    });

    // Now add ALL available guardrails with appropriate true/false values
    availableGuardrails.forEach((guardrail) => {
      // Skip the main ENABLE_GUARDRAILS toggle as it's already added
      if (guardrail.name === 'Enable Guardrails') {
        return;
      }

      const configName = `GUARDRAIL_${guardrail.name
        .toUpperCase()
        .replace(/\s+/g, '_')
        .replace(/[^A-Z0-9_]/g, '')}`;

      // Check if this guardrail is selected
      const isSelected = selectedGuardrailNames.has(configName);

      configs.push({
        configurationName: configName,
        configurationValue: isSelected ? true : false, // Use boolean values for guardrails
      });
    });

    console.log('Final guardrail configs:', configs);
    return configs;
  }

  // Enhanced validation for individual agent with comprehensive checks
  private validateIndividualAgentData(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if agent name is provided (required)
    if (!this.agentName || this.agentName.trim() === '') {
      errors.push('Agent name is required');
    }

    // Check if agent details are provided (required)
    if (!this.agentDetail || this.agentDetail.trim() === '') {
      errors.push('Agent details are required');
    }

    // Check if prompt is selected (required)
    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    if (!promptNode) {
      errors.push('Prompt selection is required');
    }

    // Check if model is selected (recommended)
    const modelNode = this.buildAgentNodes.find(
      (node) => node.type === 'model',
    );
    if (!modelNode) {
      warnings.push('Model selection is recommended for better performance');
    }

    // Check if knowledge base is selected (optional but recommended)
    const knowledgeNode = this.buildAgentNodes.find(
      (node) => node.type === 'knowledge',
    );
    if (!knowledgeNode) {
      warnings.push(
        'Knowledge base selection is recommended for enhanced responses',
      );
    }

    // Check if guardrails are selected (recommended for safety)
    const guardrailNode = this.buildAgentNodes.find(
      (node) => node.type === 'guardrail',
    );
    if (!guardrailNode) {
      warnings.push(
        'Guardrail selection is recommended for safe AI interactions',
      );
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings,
    };
  }

  // Build the complete payload for individual agent save
  private buildIndividualAgentPayload(): any {
    const { orgPath, levelId } = this.getMetadataFromNavbar();
    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );

    // Get actual prompt data
    let promptDetails = '';
    let promptTemplate = '';

    if (promptNode) {
      // Extract the numeric ID from the node ID
      const promptId = this.extractPromptId(promptNode.id);

      // Find the actual prompt data by ID or name
      const promptData = this.allToolItems['prompts'].find(
        (prompt) =>
          prompt.id === promptId ||
          prompt.id === promptNode.id ||
          prompt.name === promptNode.name,
      );

      if (promptData) {
        promptDetails = promptData.description || promptData.name || '';
        promptTemplate =
          (promptData as any).template ||
          (promptData as any).content ||
          (promptData as any).promptTemplate ||
          promptData.name ||
          '';
      } else {
        promptDetails = promptNode.name;
        promptTemplate = promptNode.name;
      }
    }

    // Format the code similar to your old repo pattern
    const useCaseCode = this.agentName
      .trim()
      .replace(/\s+/g, '_')
      .toUpperCase();

    console.log('Building payload with agentDetail:', this.agentDetail);

    // Use agentDetail for useCaseDetails if provided, otherwise use promptDetails
    const finalUseCaseDetails = this.agentDetail
      ? this.agentDetail.trim()
      : promptDetails;

    // Build organization path with use case code at the beginning
    const baseOrgPath =
      orgPath || 'ADD_NEW@SENIOR_BUSINESS_ANALYST@TEST_GOPAL@TEAM1';
    const finalOrgPath = `${useCaseCode}@${baseOrgPath}`;

    return {
      levelId: levelId,
      code: useCaseCode,
      name: this.agentName.trim(),
      useCaseDetails: finalUseCaseDetails,
      promptTemplate: promptTemplate,
      type: null,
      webPortalUseCase: null,
      location: null,
      sourceLanguage: null,
      destinationLanguage: null,
      configuration: this.buildConfigurationArrays(),
      userSignature: this.getUserSignature(),
      organizationPath: finalOrgPath,
    };
  }

  // Fetch detailed data for selected nodes to ensure we have complete information
  // Since we're using labels API, we already have all the data we need
  // No need to fetch detailed data separately
  private async fetchDetailedNodeData(): Promise<void> {
    // All data is already available from the labels API
  }

  // Fetch prompt details for collaborative agent save
  private async fetchPromptDetailsForSave(): Promise<void> {
    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    if (!promptNode) {
      console.log('No prompt node found for fetching details');
      return; // No prompt selected
    }

    // Try to find prompt in existing data first
    let promptData = this.allToolItems['prompts']?.find(
      (p) => p.name === promptNode.name,
    );

    if (!promptData && promptNode.originalToolData) {
      // Use the original tool data if available
      promptData = promptNode.originalToolData;
    }

    if (!promptData) {
      // If not found in existing data, try to extract ID and fetch from API
      const extractedId = this.extractPromptId(promptNode.id);

      if (extractedId && extractedId !== promptNode.id) {
        try {
          const apiPromptData = await new Promise((resolve, reject) => {
            this.promptsService.getPromptById(extractedId).subscribe({
              next: (data) => resolve(data),
              error: (error) => {
                console.error('Error fetching prompt from API:', error);
                reject(error);
              },
            });
          });

          if (apiPromptData) {
            // Add the fetched prompt data to allToolItems for use in payload building
            if (!this.allToolItems['prompts']) {
              this.allToolItems['prompts'] = [];
            }
            this.allToolItems['prompts'].push(apiPromptData as any);
          }
        } catch (error) {
          console.error('Error fetching prompt details:', error);
        }
      }
    }
  }

  // Save individual agent with enhanced validation and warning handling
  private async saveIndividualAgent(): Promise<void> {
    if (this.currentAgentType !== 'individual') {
      return; // Only save for individual agents
    }

    // Validate required fields first
    const validation = this.validateIndividualAgentData();

    // If there are errors, show error popup and prevent save
    if (!validation.isValid) {
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    // If there are warnings, show warning popup and ask for confirmation
    if (validation.warnings && validation.warnings.length > 0) {
      const warningMessage =
        'The following configurations are recommended:\n' +
        validation.warnings.join('\n') +
        '\n\nDo you want to continue saving without these configurations?';
      this.showWarningMessage('Configuration Recommendations', warningMessage);
      return; // Stop here and wait for user confirmation
    }

    // Proceed with save if no errors or warnings
    await this.performIndividualAgentSave();
  }

  // Perform the actual save operation for individual agent
  private async performIndividualAgentSave(): Promise<void> {
    try {
      // First fetch detailed data for all selected nodes
      await this.fetchDetailedNodeData();

      // Then build the payload with complete data
      const payload = this.buildIndividualAgentPayload();

      this.agentService.individualAgentSave(payload).subscribe({
        next: (response: any) => {
          // Show success popup with API response message
          const successMessage =
            response?.message ||
            `Individual agent "${payload.name || 'Agent'}" has been saved successfully!`;
          this.showSuccessMessage('Agent Saved Successfully', successMessage);

          // Add the newly saved agent to the dropdown immediately
          const newAgentOption = {
            value:
              payload.code || this.agentName.toUpperCase().replace(/\s+/g, ''),
            name: this.agentName,
          };

          // Check if agent already exists in dropdown
          const existingIndex = this.promptOptions.findIndex(
            (option) =>
              option.value === newAgentOption.value ||
              option.name === newAgentOption.name,
          );

          if (existingIndex === -1) {
            // Add new agent to dropdown (after the default option)
            this.promptOptions.splice(1, 0, newAgentOption);
          } else {
            // Update existing agent
            this.promptOptions[existingIndex] = newAgentOption;
          }

          // Reload agent dropdown to ensure consistency with backend
          this.loadAgentNamesForPromptOptions();

          // Auto-open playground after successful save
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error saving individual agent:', error);
          // Show error popup with API error message
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error saving individual agent. Please try again.';
          this.showErrorMessage('Save Failed', errorMessage);
        },
      });
    } catch (error) {
      console.error('Error preparing agent data for save:', error);
      this.showErrorMessage(
        'Save Failed',
        'Error preparing agent data for save. Please check console for details.',
      );
    }
  }

  // Save collaborative agent with enhanced validation and warning handling
  private async saveCollaborativeAgent(): Promise<void> {
    if (this.currentAgentType !== 'collaborative') {
      return; // Only save for collaborative agents
    }

    // Validate required fields first
    const validation = this.validateCollaborativeAgentData();

    // If there are errors, show error popup and prevent save
    if (!validation.isValid) {
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    // If there are warnings, show warning popup and ask for confirmation
    if (validation.warnings && validation.warnings.length > 0) {
      const warningMessage =
        'The following configurations are recommended:\n' +
        validation.warnings.join('\n') +
        '\n\nDo you want to continue saving without these configurations?';
      this.showWarningMessage('Configuration Recommendations', warningMessage);
      return; // Stop here and wait for user confirmation
    }

    // Proceed with save if no errors or warnings
    await this.performCollaborativeAgentSave();
  }

  // Perform the actual save operation for collaborative agent
  private async performCollaborativeAgentSave(): Promise<void> {
    try {
      // First fetch prompt details if prompt is selected
      await this.fetchPromptDetailsForSave();

      // Then build the payload with complete data including prompt details
      const payload = this.buildCollaborativeAgentPayload();

      this.agentService.collaborativeAgentSave(payload).subscribe({
        next: (response: any) => {
          // Show success popup with API response message
          const successMessage =
            response?.message ||
            `Collaborative agent "${payload.name || 'Agent'}" has been saved successfully!`;
          this.showSuccessMessage('Agent Saved Successfully', successMessage);

          // Add the newly saved agent to the dropdown immediately
          const newAgentOption = {
            value: this.agentName.toUpperCase().replace(/\s+/g, ''),
            name: this.agentName,
          };

          // Check if agent already exists in dropdown
          const existingIndex = this.promptOptions.findIndex(
            (option) =>
              option.value === newAgentOption.value ||
              option.name === newAgentOption.name,
          );

          if (existingIndex === -1) {
            // Add new agent to dropdown (after the default option)
            this.promptOptions.splice(1, 0, newAgentOption);
          } else {
            // Update existing agent
            this.promptOptions[existingIndex] = newAgentOption;
          }

          // Reload agent dropdown to ensure consistency with backend
          this.loadAgentNamesForPromptOptions();

          // Auto-open playground after successful save
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error saving collaborative agent:', error);
          // Show error popup with API error message
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error saving collaborative agent. Please try again.';
          this.showErrorMessage('Save Failed', errorMessage);
        },
      });
    } catch (error) {
      console.error(
        'Error preparing collaborative agent data for save:',
        error,
      );
      this.showErrorMessage(
        'Save Failed',
        'Error preparing collaborative agent data for save. Please check console for details.',
      );
    }
  }

  // Enhanced validation for collaborative agent with comprehensive checks
  private validateCollaborativeAgentData(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if agent name is provided (required)
    if (!this.agentName || this.agentName.trim().length === 0) {
      errors.push('Agent name is required');
    }

    // Check if agent details are provided (required)
    if (!this.agentDetail || this.agentDetail.trim().length === 0) {
      errors.push('Agent details are required');
    }

    // Check if at least one prompt is selected (required)
    const promptNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'prompt',
    );
    if (promptNodes.length === 0) {
      errors.push('Prompt selection is required');
    }

    // Check if at least one model is selected (required)
    const modelNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'model',
    );
    if (modelNodes.length === 0) {
      errors.push('Model selection is required');
    }

    // Check if knowledge base is selected (recommended - warning only)
    const knowledgeNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'knowledge',
    );
    if (knowledgeNodes.length === 0) {
      warnings.push(
        'Knowledge base selection is recommended for enhanced responses',
      );
    }

    // Check if tools are selected (recommended - warning only)
    const toolNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'tool',
    );
    if (toolNodes.length === 0) {
      warnings.push(
        'Tool selection is recommended for collaborative agent capabilities',
      );
    }

    // Note: Guardrails are not included for collaborative agents as per user requirement

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // Build the complete payload for collaborative agent save
  private buildCollaborativeAgentPayload(): any {
    const {
      levelId,
      orgId,
      domainId,
      projectId,
      teamId,
      org,
      domain,
      project,
      team,
    } = this.getMetadataFromNavbar();

    // Get model reference
    const modelNode = this.buildAgentNodes.find(
      (node) => node.type === 'model',
    );
    const modelData = modelNode
      ? this.allToolItems['models'].find((m) => m.name === modelNode.name)
      : null;
    const modelRef = modelData?.id || 32; // Default model ID

    // Get knowledge base references (remove duplicates)
    const knowledgeNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'knowledge',
    );
    const knowledgeIds = knowledgeNodes
      .map((node) => {
        const knowledgeData = this.allToolItems['knowledge'].find(
          (k) => k.name === node.name,
        );
        return knowledgeData?.id;
      })
      .filter((id) => id);

    // Remove duplicates and join
    const uniqueKnowledgeIds = [...new Set(knowledgeIds)];
    const knowledgeBaseRef = uniqueKnowledgeIds.join(',');

    // Get prompt data for role, goal, backstory, description, expectedOutput
    const promptNode = this.buildAgentNodes.find(
      (node) => node.type === 'prompt',
    );
    let promptData = null;

    if (promptNode) {
      // First try to use the original tool data if available
      if (promptNode.originalToolData) {
        promptData = promptNode.originalToolData;
      } else {
        // Fallback: try to find by name
        promptData = this.allToolItems['prompts'].find(
          (p) => p.name === promptNode.name,
        );

        // If not found by name, try to find by extracting ID from node ID
        if (!promptData) {
          const extractedId = this.extractPromptId(promptNode.id);
          promptData = this.allToolItems['prompts'].find((p) => {
            return (
              String(p.id) === extractedId ||
              String(p.id) === String(parseInt(extractedId))
            );
          });
        }
      }
    }

    // Check if we have original prompt data
    const originalPromptData = promptData?.originalPromptData || promptData;

    // Extract prompt details with proper fallbacks, using original data if available
    const sourceData = originalPromptData || promptData;
    const role = sourceData?.role || 'admin new 2';
    const goal = sourceData?.goal || this.agentDetail || 'Default goal';
    const backstory =
      sourceData?.backstory || this.agentDetail || 'Default backstory';
    const description =
      sourceData?.description ||
      sourceData?.descriptionConsolidated ||
      this.agentDetail ||
      'Default description';
    const expectedOutput =
      sourceData?.expectedOutput ||
      sourceData?.expectedOutputConsolidated ||
      this.agentDetail ||
      'Default expected output';

    // Get tools
    const toolNodes = this.buildAgentNodes.filter(
      (node) => node.type === 'tool',
    );
    const tools = toolNodes.map((node) => {
      const toolData = this.allToolItems['tools'].find(
        (t) => t.name === node.name,
      );
      // Extract numeric ID from tool ID (remove prefixes like "builtin-", "user-")
      let toolId = toolData?.id || '7';
      if (typeof toolId === 'string') {
        const numericMatch = toolId.match(/\d+$/);
        if (numericMatch) {
          toolId = numericMatch[0];
        }
      }
      return {
        toolId: toolId,
        toolName: toolData?.name || node.name || 'FileWriterTool',
        parameters: [],
      };
    });

    // Get agent configurations from model node
    const agentConfigs = {
      temperature: modelData?.['temperature'] || 0.3,
      topP: modelData?.['topP'] || 0.95,
      maxToken: modelData?.['maxToken'] || 1500,
    };

    const payload = {
      levelId: levelId,
      agentDetail: this.agentDetail.trim(),
      name: this.agentName.trim(),
      modelRef: modelRef,
      knowledgeBaseRef: knowledgeBaseRef || '', // Ensure it's not undefined
      role: role,
      goal: goal,
      backstory: backstory,
      description: description,
      expectedOutput: expectedOutput,
      agentConfigs: agentConfigs,
      maxIter: 3,
      maxRpm: null,
      maxExecutionTime: 60,
      verbose: true,
      allowDelegation: true,
      allowCodeExecution: false,
      isSafeCodeExecution: true,
      userSignature: this.getUserSignature(),
      tools: tools,
      userTools: [],
      // Add organizational fields
      orgId: orgId,
      domainId: domainId,
      projectId: projectId,
      teamId: teamId,
      org: org,
      domain: domain,
      project: project,
      team: team,
    };

    // Payload built successfully

    return payload;
  }

  // Build the complete payload for individual agent update (with configId fields)
  private buildIndividualAgentUpdatePayload(): any {
    const basePayload = this.buildIndividualAgentPayload();

    // Add useCaseId for update
    const updatePayload = {
      ...basePayload,
      useCaseId: parseInt(this.currentAgentId!), // Convert to number for update
      // Add configId fields to configuration items
      configuration: basePayload.configuration.map((category: any) => ({
        ...category,
        configs: category.configs.map((config: any) => ({
          ...config,
          configId:
            this.agentConfigIds.get(
              `${category.categoryId}-${config.configurationName}`,
            ) || undefined,
        })),
      })),
    };

    // Individual agent update payload built successfully
    return updatePayload;
  }

  // Build the complete payload for collaborative agent update (with configId fields)
  private buildCollaborativeAgentUpdatePayload(): any {
    const basePayload = this.buildCollaborativeAgentPayload();

    // Add agentId for update
    const updatePayload = {
      ...basePayload,
      agentId: parseInt(this.currentAgentId!), // Convert to number for update
      // Add any additional fields needed for collaborative agent update
    };

    // Update payload built successfully
    return updatePayload;
  }

  // Debug method to log current canvas state
  private logCanvasState(): void {
    // Canvas state logging for debugging
  }

  // Update individual agent (PUT request)
  private async updateIndividualAgent(): Promise<void> {
    if (this.currentAgentType !== 'individual' || !this.currentAgentId) {
      return;
    }

    // Validate required fields first
    const validation = this.validateIndividualAgentData();
    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors);
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    try {
      // First fetch detailed data for all selected nodes
      await this.fetchDetailedNodeData();

      // Build the payload with configId fields for update
      const payload = this.buildIndividualAgentUpdatePayload();

      this.agentService.individualAgentEdit(payload).subscribe({
        next: (response: any) => {
          // Show success popup with API response message
          const successMessage =
            response?.message ||
            `Individual agent "${payload.name || 'Agent'}" has been updated successfully!`;
          this.showSuccessMessage('Agent Updated Successfully', successMessage);

          // Reload agent dropdown to ensure consistency with backend
          this.loadAgentNamesForPromptOptions();

          // Auto-open playground after successful update
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error updating individual agent:', error);
          // Show error popup with API error message
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error updating individual agent. Please try again.';
          this.showErrorMessage('Update Failed', errorMessage);
        },
      });
    } catch (error) {
      console.error('Error preparing agent data for update:', error);
      this.showErrorMessage(
        'Update Failed',
        'Error preparing agent data for update. Please check console for details.',
      );
    }
  }

  // Update collaborative agent (PUT request)
  private async updateCollaborativeAgent(): Promise<void> {
    if (this.currentAgentType !== 'collaborative' || !this.currentAgentId) {
      return;
    }

    // Validate required fields first
    const validation = this.validateCollaborativeAgentData();
    if (!validation.isValid) {
      console.error('Validation failed:', validation.errors);
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors:\n' + validation.errors.join('\n'),
      );
      return;
    }

    try {
      // First fetch detailed data for all selected nodes
      await this.fetchDetailedNodeData();

      // Build the payload with configId fields for update
      const payload = this.buildCollaborativeAgentUpdatePayload();

      this.agentService.collaborativeAgentEdit(payload).subscribe({
        next: (response: any) => {
          // Show success popup with API response message
          const successMessage =
            response?.message ||
            `Collaborative agent "${payload.name || 'Agent'}" has been updated successfully!`;
          this.showSuccessMessage('Agent Updated Successfully', successMessage);

          // Reload agent dropdown to ensure consistency with backend
          this.loadAgentNamesForPromptOptions();

          // Auto-open playground after successful update
          setTimeout(() => {
            this.onExecute();
          }, 1000);
        },
        error: (error: any) => {
          console.error('Error updating collaborative agent:', error);
          // Show error popup with API error message
          const errorMessage =
            error?.error?.message ||
            error?.message ||
            'Error updating collaborative agent. Please try again.';
          this.showErrorMessage('Update Failed', errorMessage);
        },
      });
    } catch (error) {
      console.error(
        'Error preparing collaborative agent data for update:',
        error,
      );
      this.showErrorMessage(
        'Update Failed',
        'Error preparing collaborative agent data for update. Please check console for details.',
      );
    }
  }

  // Handle primary button click from canvas-board
  async onPrimaryButtonClick(): Promise<void> {
    // Debug current state
    this.logCanvasState();

    if (this.isExecuteMode) {
      // In execute mode, "Run" button should just run the agent
      console.log('Running agent in execute mode');
      return;
    }

    if (this.isViewMode) {
      // In view mode, "Execute" button should switch to execute mode
      this.onExecute();
      return;
    }

    if (this.isEditMode) {
      // In edit mode, "Update" button should update the agent
      if (this.currentAgentType === 'individual') {
        await this.updateIndividualAgent();
      } else if (this.currentAgentType === 'collaborative') {
        await this.updateCollaborativeAgent();
      }
      return;
    }

    // Default: new agent creation or duplicate - "Save" (without auto-execute)
    // Note: Duplicate mode is treated as new agent creation
    // Validate first before attempting to save
    const validation =
      this.currentAgentType === 'individual'
        ? this.validateIndividualAgentData()
        : this.validateCollaborativeAgentData();

    // If there are errors, show error popup and prevent save/execute
    if (!validation.isValid) {
      this.showErrorMessage(
        'Validation Failed',
        'Please fix the following errors before saving:\n' +
          validation.errors.join('\n'),
      );
      return;
    }

    // Proceed with save - the save methods will handle warnings internally
    if (this.currentAgentType === 'individual') {
      await this.saveIndividualAgent();
    } else if (this.currentAgentType === 'collaborative') {
      await this.saveCollaborativeAgent();
    }

    // Note: Playground will only open after successful save via success popup or manual execute
  }

  // Execute functionality - show playground component
  onExecute(): void {
    // Set flags to show playground interface
    this.isExecuteMode = true;
    this.showChatInterface = true;

    // Recalculate node positions for execute mode (vertical layout)
    this.recalculateAllNodePositions();

    // Force connection points update after a delay to ensure DOM is updated
    setTimeout(() => {
      this.cdr.detectChanges();
      // Force update of connection points for execute mode
      if (this.canvasBoardComponent) {
        this.canvasBoardComponent.updateNodeConnectionPoints();
      }
    }, 200);

    // Load all agents for prompt options dropdown
    this.loadAgentNamesForPromptOptions();

    // Auto-select current agent (for both existing and newly saved agents)
    if (this.agentName) {
      setTimeout(() => {
        this.autoSelectCurrentAgent();
      }, 1500); // Wait for agents to load and dropdown to populate
    }

    // Initialize the chat interface
    this.chatMessages = [
      {
        from: 'ai',
        text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`,
      },
    ];

    // Start execution service
    setTimeout(() => {
      const agentId = 'build-agent-' + Date.now(); // Generate a unique ID for this session
      this.toolExecutionService.startExecution(agentId, this.chatMessages);

      // Subscribe to execution state changes
      this.executionSubscription = this.toolExecutionService
        .getExecutionState()
        .subscribe((state) => {
          if (state.isExecuting && state.toolId === agentId) {
            this.chatMessages = state.chatMessages;
          }
        });
    }, 100);
  }

  // Handle chat messages from playground with real API integration
  handleChatMessage(message: string): void {
    // Check if an agent is selected from the dropdown
    if (!this.selectedPrompt || this.selectedPrompt === 'default') {
      this.showAgentError(
        'Please select an agent from the dropdown before testing.',
      );
      return;
    }

    // Prepare user message with file attachments info
    let displayMessage = message;
    if (this.agentFilesUploadedData.length > 0) {
      const fileNames = this.agentFilesUploadedData
        .map((file) => file.documentName)
        .join(', ');
      displayMessage = `${message}\n\n📎 Attached files: ${fileNames}`;
    }

    // Add user message to chat immediately (regardless of conversational mode)
    this.chatMessages = [
      ...this.chatMessages,
      {
        from: 'user',
        text: displayMessage,
      },
    ];

    this.isProcessingChat = true;

    // Get form values for conversational and template modes
    const isConversational =
      this.agentPlaygroundForm.get('isConversational')?.value || false;
    const isUseTemplate =
      this.agentPlaygroundForm.get('isUseTemplate')?.value || false;

    // Use the agent code for API calls (not the display name)
    const agentMode =
      this.agentCode || this.selectedAgentMode || this.selectedPrompt;

    // Use the useCaseIdentifier from agent data if available, otherwise build it
    let useCaseIdentifier = this.selectedUseCaseIdentifier;
    if (!useCaseIdentifier) {
      // Fallback: Build useCaseIdentifier using actual organization path from metadata
      const orgPath = this.buildOrganizationPath();
      useCaseIdentifier = `${agentMode}${orgPath}`;
    }

    // Handle file uploads first if present
    if (this.agentFilesUploadedData.length > 0) {
      this.processAgentFilesAndSendMessage(
        message,
        agentMode,
        useCaseIdentifier,
        isConversational,
        isUseTemplate,
      );
      return;
    }

    this.sendAgentMessageToAPI(
      message,
      agentMode,
      useCaseIdentifier,
      isConversational,
      isUseTemplate,
    );
  }

  private processAgentFilesAndSendMessage(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
  ): void {
    const formData = new FormData();

    // Add actual files to FormData
    this.agentFilesUploadedData.forEach((fileData) => {
      if (fileData.file) {
        formData.append('files', fileData.file);
      }
    });

    if (formData.has('files')) {
      this.agentPlaygroundService
        .getFileToContent(formData)
        .pipe(
          switchMap((fileResponse) => {
            const fileContent =
              fileResponse?.fileResponses
                ?.map((response: any) => response.fileContent)
                ?.join('\n') || '';
            console.log('File content extracted:', fileContent);

            // Send message with file content attached
            this.sendAgentMessageToAPIWithFiles(
              message,
              mode,
              useCaseIdentifier,
              isConversational,
              isUseTemplate,
              fileContent,
            );
            return of(null);
          }),
          catchError((error) => {
            console.error('Error parsing files:', error);
            this.sendAgentMessageToAPI(
              message,
              mode,
              useCaseIdentifier,
              isConversational,
              isUseTemplate,
            );
            return of(null);
          }),
        )
        .subscribe();
    } else {
      this.sendAgentMessageToAPI(
        message,
        mode,
        useCaseIdentifier,
        isConversational,
        isUseTemplate,
      );
    }
  }

  private sendAgentMessageToAPI(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
  ): void {
    // Add user message to chat payload for conversational mode
    if (isConversational) {
      this.agentChatPayload.push({ content: message, role: 'user' });
    }

    // Prepare payload based on mode
    const payload = isConversational ? this.agentChatPayload : message;

    this.agentPlaygroundService
      .generatePrompt(
        payload,
        mode,
        isConversational,
        isUseTemplate,
        this.agentAttachment,
        useCaseIdentifier,
      )
      .pipe(
        finalize(() => this.resetAgentMessageState()),
        takeUntil(this.agentPlaygroundDestroy),
      )
      .subscribe({
        next: (generatedResponse: any) => {
          if (
            generatedResponse?.response &&
            generatedResponse?.response?.choices
          ) {
            const aiResponseText = generatedResponse.response.choices[0].text;

            // Add AI response to messages
            this.chatMessages = [
              ...this.chatMessages,
              {
                from: 'ai',
                text: aiResponseText,
              },
            ];

            // Add to chat payload for conversational mode
            if (isConversational) {
              this.agentChatPayload.push({
                content: aiResponseText,
                role: 'assistant',
              });
            }
          } else {
            console.warn('Unexpected API response format:', generatedResponse);
            this.showAgentError(
              'Received unexpected response format from API.',
            );
          }
        },
        error: (error: any) => {
          console.error('API Error:', error);
          const errorMessage =
            error?.error?.message ||
            'An error occurred while processing your request.';
          this.showAgentError(errorMessage);

          // Remove the user message from chat payload if it was added
          if (isConversational && this.agentChatPayload.length > 0) {
            this.agentChatPayload.pop();
          }
        },
      });
  }

  private sendAgentMessageToAPIWithFiles(
    message: string,
    mode: string,
    useCaseIdentifier: string,
    isConversational: boolean,
    isUseTemplate: boolean,
    fileContents: string,
  ): void {
    // Add user message to chat payload for conversational mode
    if (isConversational) {
      this.agentChatPayload.push({ content: message, role: 'user' });
    }

    // Prepare payload based on mode
    const payload = isConversational ? this.agentChatPayload : message;

    console.log('Calling API with payload and files:', {
      payload,
      mode,
      useCaseIdentifier,
      isConversational,
      isUseTemplate,
      fileContents: fileContents ? 'File content included' : 'No files',
    });

    this.agentPlaygroundService
      .generatePrompt(
        payload,
        mode,
        isConversational,
        isUseTemplate,
        this.agentAttachment,
        useCaseIdentifier,
        fileContents,
      )
      .pipe(
        finalize(() => this.resetAgentMessageState()),
        takeUntil(this.agentPlaygroundDestroy),
      )
      .subscribe({
        next: (generatedResponse: any) => {
          console.log('API Response received:', generatedResponse);

          if (
            generatedResponse?.response &&
            generatedResponse?.response?.choices
          ) {
            const aiResponseText = generatedResponse.response.choices[0].text;

            // Add AI response to messages
            this.chatMessages = [
              ...this.chatMessages,
              {
                from: 'ai',
                text: aiResponseText,
              },
            ];

            // Add to chat payload for conversational mode
            if (isConversational) {
              this.agentChatPayload.push({
                content: aiResponseText,
                role: 'assistant',
              });
            }
          } else {
            console.warn('Unexpected API response format:', generatedResponse);
            this.showAgentError(
              'Received unexpected response format from API.',
            );
          }
        },
        error: (error: any) => {
          console.error('API Error:', error);
          const errorMessage =
            error?.error?.message ||
            'An error occurred while processing your request.';
          this.showAgentError(errorMessage);

          // Remove the user message from chat payload if it was added
          if (isConversational && this.agentChatPayload.length > 0) {
            this.agentChatPayload.pop();
          }
        },
      });
  }

  private resetAgentMessageState(): void {
    this.isProcessingChat = false;
    this.isAgentPlaygroundLoading = false;
  }

  private showAgentError(message: string): void {
    this.chatMessages = [
      ...this.chatMessages,
      {
        from: 'ai',
        text: message,
      },
    ];
  }

  // Handle toggle events from playground component
  onPlaygroundConversationalToggle(event: boolean): void {
    this.agentPlaygroundForm.get('isConversational')?.setValue(event);
    console.log('Playground Conversational toggle:', event);
  }

  onPlaygroundTemplateToggle(event: boolean): void {
    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(event);
    console.log('Playground Template toggle:', event);
  }

  // Handle file selection from playground
  onFilesSelected(files: any[]): void {
    this.agentFilesUploadedData = files;
    console.log('Files selected from playground:', files);
  }

  // Agent Playground Toggle Methods (kept for compatibility)
  onAgentConversationalToggle(event: any): void {
    const isConversational = event;
    this.agentPlaygroundForm
      .get('isConversational')
      ?.setValue(isConversational);

    // If conversational is enabled, disable template
    if (
      isConversational &&
      this.agentPlaygroundForm.get('isUseTemplate')?.value
    ) {
      this.agentPlaygroundForm.get('isUseTemplate')?.setValue(false);
    }

    console.log('Agent Conversational mode:', isConversational);
  }

  onAgentTemplateToggle(event: any): void {
    const isUseTemplate = event;
    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(isUseTemplate);

    // If template is enabled, disable conversational
    if (
      isUseTemplate &&
      this.agentPlaygroundForm.get('isConversational')?.value
    ) {
      this.agentPlaygroundForm.get('isConversational')?.setValue(false);
    }

    console.log('Agent Use template:', isUseTemplate);
  }

  // Agent File Upload Methods
  onAgentFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.agentFilesUploadedData = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        this.agentFilesUploadedData.push({
          id: `agent_file_${Date.now()}_${i}`,
          documentName: file.name,
          isImage: file.type.startsWith('image/'),
          file: file,
        });
      }

      console.log('Agent files selected:', this.agentFilesUploadedData);
    }
  }

  removeAgentFile(index: number): void {
    this.agentFilesUploadedData.splice(index, 1);
  }

  // Clear agent chat data
  clearAgentChatData(): void {
    this.chatMessages = [
      {
        from: 'ai',
        text: 'Hi there, how can I help you test your agent today?',
      },
    ];
    this.agentChatPayload = [];
    this.agentFilesUploadedData = [];
    this.agentAttachment = [];
  }

  // Show response modal with message
  showResponseModal(message: string, isError: boolean = false): void {
    this.modalMessage = message;
    this.isModalError = isError;
    this.isResponseModalOpen = true;
  }

  // Close response modal
  closeResponseModal(): void {
    this.isResponseModalOpen = false;
    this.modalMessage = '';
    this.isModalError = false;
  }

  // Load all agent names for prompt options dropdown
  public loadAgentNamesForPromptOptions(): void {
    if (this.currentAgentType === 'collaborative') {
      this.agentService.getAllCollaborativeAgents().subscribe({
        next: (response) => {
          console.log('Raw collaborative agents response:', response);
          console.log('Response type:', typeof response);
          console.log('Is array:', Array.isArray(response));

          let agents = [];

          // Handle different response structures
          if (Array.isArray(response)) {
            agents = response;
          } else if (
            response &&
            (response as any).agentDetails &&
            Array.isArray((response as any).agentDetails)
          ) {
            agents = (response as any).agentDetails;
          } else if (
            response &&
            (response as any).data &&
            Array.isArray((response as any).data)
          ) {
            agents = (response as any).data;
          } else if (response && typeof response === 'object') {
            // If response is an object, try to find the agents array
            const possibleArrays = Object.values(response).filter((val) =>
              Array.isArray(val),
            );
            if (possibleArrays.length > 0) {
              agents = possibleArrays[0] as any[];
            }
          }

          console.log('Extracted agents array:', agents);
          console.log('Number of agents found:', agents.length);

          if (agents && agents.length > 0) {
            // Map collaborative agent data to DropdownOption format
            this.promptOptions = agents.map((agent: any) => {
              console.log('Processing agent:', agent);
              return {
                value:
                  agent.name?.toUpperCase().replace(/\s+/g, '') ||
                  `AGENT_${agent.id}`,
                name: agent.name || 'Unknown Agent',
                // Store additional agent data for chat API (using any to extend interface)
                ...(agent && { agentData: agent }),
              } as any;
            });
          } else {
            console.warn('No collaborative agents found in response');
            this.promptOptions = [];
          }

          // Add a default option at the beginning
          this.promptOptions.unshift({
            value: 'default',
            name: 'Choose an Agent',
          });
          console.log('Final prompt options:', this.promptOptions);
        },
        error: (error) => {
          console.error(
            'Error loading collaborative agents for prompt options:',
            error,
          );
          console.error('Error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            url: error.url,
          });

          // Add current agent if available
          if (this.currentAgentId && this.agentName) {
            this.promptOptions.push({
              value: this.agentName.toUpperCase().replace(/\s+/g, ''),
              name: this.agentName,
            });
          }
        },
      });
    } else {
      this.agentService.getIndividualAgentMinDetails().subscribe({
        next: (response) => {
          console.log('Raw individual agents min details response:', response);
          console.log('Response type:', typeof response);
          console.log('Is response an array?', Array.isArray(response));

          let agents = [];

          // Based on your sample response, it should be a direct array
          // Handle response structure - expecting array of agent objects
          if (Array.isArray(response)) {
            agents = response;
            console.log(
              'Response is directly an array with',
              response.length,
              'agents',
            );
          } else if (response && Array.isArray(response.data)) {
            agents = response.data;
            console.log(
              'Found agents in response.data with',
              response.data.length,
              'agents',
            );
          } else if (response && Array.isArray(response.agents)) {
            agents = response.agents;
            console.log(
              'Found agents in response.agents with',
              response.agents.length,
              'agents',
            );
          } else {
            console.warn('Response structure not recognized:', response);
            console.warn(
              'Response keys:',
              response ? Object.keys(response) : 'No response',
            );
            // Try to find any array in the response
            if (response && typeof response === 'object') {
              const possibleArrays = Object.values(response).filter((val) =>
                Array.isArray(val),
              );
              if (possibleArrays.length > 0) {
                agents = possibleArrays[0] as any[];
                console.log(
                  'Found agents in first array property with',
                  agents.length,
                  'agents',
                );
              }
            }
          }

          console.log('Extracted individual agents array:', agents);
          console.log('Number of individual agents found:', agents.length);

          if (agents && agents.length > 0) {
            // Map individual agent data to DropdownOption format using the new API structure
            this.promptOptions = agents.map((agent: any) => {
              console.log('Processing individual agent:', agent);
              const option = {
                value: agent.code || agent.id?.toString() || 'DEFAULT_AGENT',
                name: agent.name || 'Unknown Agent',
                // Store additional agent data for chat API (using any to extend interface)
                ...(agent && { agentData: agent }),
              } as any;
              console.log('Created option:', option);
              return option;
            });

            console.log(
              'All mapped options before adding default:',
              this.promptOptions,
            );
          } else {
            console.warn(
              'No individual agents found in response, initializing with sample data',
            );
            // If no agents found, create some sample options for testing
            this.promptOptions = [
              {
                value: 'CREATE_NEW_THREE',
                name: 'create new three',
                agentData: {
                  id: '55',
                  code: 'CREATE_NEW_THREE',
                  name: 'create new three',
                  useCaseIdentifier:
                    'CREATE_NEW_THREE@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER',
                },
              },
              {
                value: 'CREATE_NEW_TWO',
                name: 'create new two',
                agentData: {
                  id: '54',
                  code: 'CREATE_NEW_TWO',
                  name: 'create new two',
                  useCaseIdentifier:
                    'CREATE_NEW_TWO@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER',
                },
              },
            ] as any[];
            console.log('Added sample agents for testing:', this.promptOptions);
          }

          // Add current agent to options if not already present (for edit/view mode)
          if (this.currentAgentId && this.agentName) {
            const currentAgentExists = this.promptOptions.find(
              (option) =>
                option.name === this.agentName ||
                option.value.toString() === this.currentAgentId ||
                option.value.toString() ===
                  this.agentName.toUpperCase().replace(/\s+/g, ''),
            );

            if (!currentAgentExists) {
              // Add current agent to the options
              this.promptOptions.push({
                value:
                  this.agentCode ||
                  this.agentName.toUpperCase().replace(/\s+/g, ''),
                name: this.agentName,
                // Store additional agent data for chat API (using any to extend interface)
                agentData: {
                  id: this.currentAgentId,
                  code: this.agentCode,
                  name: this.agentName,
                  useCaseIdentifier: this.selectedUseCaseIdentifier,
                },
              } as any);
              console.log(
                'Added current agent to dropdown options:',
                this.agentName,
              );
            }
          }

          // Add a default option at the beginning
          this.promptOptions.unshift({
            value: 'default',
            name: 'Choose an Agent',
          });

          console.log(
            'Final individual agent options for playground:',
            this.promptOptions,
          );
          console.log('Total options count:', this.promptOptions.length);

          // Force change detection to update the dropdown
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error(
            'Error loading individual agents min details for prompt options:',
            error,
          );
          console.error('Error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            url: error.url,
          });

          // Fallback to old API if new API fails
          console.log('Falling back to old individual agents API...');
          this.loadIndividualAgentsFromOldAPI();

          // Force change detection
          this.cdr.detectChanges();
        },
      });
    }
  }

  // Fallback method to load individual agents using the old API
  private loadIndividualAgentsFromOldAPI(): void {
    console.log('Loading individual agents using old API...');
    this.agentService.getAllAgentList().subscribe({
      next: (response) => {
        if (response && response.agentWithUseCaseDetails) {
          // Map agent data to DropdownOption format with proper mode values
          this.promptOptions = response.agentWithUseCaseDetails
            .filter((agent: any) => agent.type === 'individual') // Only individual agents
            .map(
              (agent: any) =>
                ({
                  value:
                    agent.code ||
                    agent.name?.toUpperCase().replace(/\s+/g, '') ||
                    'DEFAULT_AGENT',
                  name: agent.name || 'Unknown Agent',
                  // Store additional agent data for chat API (using any to extend interface)
                  ...(agent && { agentData: agent }),
                }) as any,
            );

          // Add current agent to options if not already present (for edit/view mode)
          if (this.currentAgentId && this.agentName) {
            const currentAgentExists = this.promptOptions.find(
              (option) =>
                option.name === this.agentName ||
                option.value.toString() === this.currentAgentId ||
                option.value.toString() ===
                  this.agentName.toUpperCase().replace(/\s+/g, ''),
            );

            if (!currentAgentExists) {
              // Add current agent to the options
              this.promptOptions.push({
                value:
                  this.agentCode ||
                  this.agentName.toUpperCase().replace(/\s+/g, ''),
                name: this.agentName,
                // Store additional agent data for chat API (using any to extend interface)
                agentData: {
                  id: this.currentAgentId,
                  code: this.agentCode,
                  name: this.agentName,
                  useCaseIdentifier: this.selectedUseCaseIdentifier,
                },
              } as any);
              console.log(
                'Added current agent to dropdown options:',
                this.agentName,
              );
            }
          }

          // Add a default option at the beginning
          this.promptOptions.unshift({
            value: 'default',
            name: 'Choose an Agent',
          });

          console.log(
            'Loaded individual agent options for playground (fallback):',
            this.promptOptions,
          );
        } else {
          console.warn('No individual agents found in fallback response');
          // Fallback to default options
          this.promptOptions = [{ value: 'default', name: 'Choose an Agent' }];

          // Add current agent if available
          if (this.currentAgentId && this.agentName) {
            this.promptOptions.push({
              value:
                this.agentCode ||
                this.agentName.toUpperCase().replace(/\s+/g, ''),
              name: this.agentName,
              agentData: {
                id: this.currentAgentId,
                code: this.agentCode,
                name: this.agentName,
                useCaseIdentifier: this.selectedUseCaseIdentifier,
              },
            } as any);
          }
        }
      },
      error: (error) => {
        console.error(
          'Error loading individual agents from fallback API:',
          error,
        );
        // Final fallback to default agents on error
        this.promptOptions = [{ value: 'default', name: 'Choose an Agent' }];

        // Add current agent if available
        if (this.currentAgentId && this.agentName) {
          this.promptOptions.push({
            value:
              this.agentCode ||
              this.agentName.toUpperCase().replace(/\s+/g, ''),
            name: this.agentName,
            agentData: {
              id: this.currentAgentId,
              code: this.agentCode,
              name: this.agentName,
              useCaseIdentifier: this.selectedUseCaseIdentifier,
            },
          } as any);
        }
      },
    });
  }

  // Auto-select current agent in the dropdown when entering execute mode
  private autoSelectCurrentAgent(): void {
    if (!this.promptOptions || this.promptOptions.length === 0) {
      console.log('No prompt options available for auto-selection');
      return;
    }

    if (!this.agentName) {
      console.log('No agent name available for auto-selection');
      return;
    }

    // Try multiple strategies to find the current agent in the prompt options
    let currentAgentOption = this.promptOptions.find(
      (option) =>
        option.name === this.agentName ||
        option.name.toLowerCase() === this.agentName.toLowerCase(),
    );

    // If not found by name, try by value
    if (!currentAgentOption) {
      const agentValue = this.agentName.toUpperCase().replace(/\s+/g, '');
      currentAgentOption = this.promptOptions.find(
        (option) =>
          option.value.toString() === agentValue ||
          option.value.toString() === this.currentAgentId,
      );
    }

    // If still not found, create a temporary option for the current agent
    if (!currentAgentOption) {
      currentAgentOption = {
        value: this.agentName.toUpperCase().replace(/\s+/g, ''),
        name: this.agentName,
      };
      console.log(
        'Created temporary option for current agent:',
        currentAgentOption,
      );
    }

    console.log('Auto-selecting current agent:', currentAgentOption);

    // Set the selected prompt value for the playground component
    this.selectedPrompt = currentAgentOption.value.toString();

    // Simulate the selection by calling onPromptChanged
    this.onPromptChanged(currentAgentOption);

    // Force update the playground component's selected value
    this.cdr.detectChanges();
  }

  // Handle prompt changes from playground
  onPromptChanged(selectedOption: DropdownOption): void {
    // Get agent data from the selected option
    const agentData = (selectedOption as any).agentData;

    if (agentData) {
      // Use dynamic agent data from the API
      console.log('Using dynamic agent data:', agentData);

      // Set the selected prompt for agent execution - use NAME for dropdown display, CODE for API calls
      this.selectedPrompt = selectedOption.name; // Use name for dropdown display
      this.agentCode = agentData.code || selectedOption.value.toString(); // Use agent code from data
      this.selectedAgentMode =
        agentData.code || selectedOption.value.toString(); // Use agent code for mode parameter

      // Build useCaseIdentifier using agent data
      if (agentData.useCaseIdentifier) {
        // Use the useCaseIdentifier from agent data if available
        this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;
      } else {
        // Build useCaseIdentifier using actual organization path from metadata
        const orgPath = this.buildOrganizationPath();
        this.selectedUseCaseIdentifier = `${this.agentCode}${orgPath}`;
      }
    } else {
      // Fallback to old behavior for backward compatibility
      console.log('No agent data found, using fallback behavior');
      const agentCode = selectedOption.value.toString();

      this.selectedPrompt = selectedOption.name;
      this.agentCode = agentCode;
      this.selectedAgentMode = agentCode;

      // Build useCaseIdentifier using actual organization path from metadata
      const orgPath = this.buildOrganizationPath();
      this.selectedUseCaseIdentifier = `${agentCode}${orgPath}`;
    }

    console.log('Agent selected for execution:', {
      name: selectedOption.name,
      code: this.agentCode,
      mode: this.selectedAgentMode,
      useCaseIdentifier: this.selectedUseCaseIdentifier,
      agentData: agentData,
    });

    // Force change detection to update the playground component
    this.cdr.detectChanges();
  }

  // Exit execute mode
  onExitExecuteMode(): void {
    this.isExecuteMode = false;
    this.showChatInterface = false;

    // Clear execute nodes and restore original canvas nodes
    this.executeNodes = [];
    this.canvasNodes = this.buildAgentNodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: 90 },
      position: node.position,
    }));

    // Recreate connections based on hierarchy
    this.createAgentFlowConnections(this.buildAgentNodes);

    // Recalculate node positions for normal mode (horizontal layout)
    this.recalculateAllNodePositions();

    // Force connection points update after a delay to ensure DOM is updated
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 200);

    // Clean up execution subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
      this.executionSubscription = undefined;
    }

    // Stop execution service
    this.toolExecutionService.stopExecution();
  }

  onCanvasStateChanged(state: {
    nodes: CanvasNode[];
    edges: CanvasEdge[];
  }): void {
    this.canvasNodes = state.nodes;
    this.canvasEdges = state.edges;

    // Sync build agent nodes with canvas nodes
    this.buildAgentNodes = state.nodes.map((canvasNode) => ({
      id: canvasNode.id,
      name: canvasNode.data.name || canvasNode.data.label,
      type: canvasNode.data.type,
      icon: canvasNode.data.icon,
      position: canvasNode.position,
    }));
  }

  // Handle agent name changes from canvas board
  onAgentNameChanged(agentName: string): void {
    console.log('Agent name changed:', agentName);
    this.agentName = agentName;
  }

  // Handle metadata changes from canvas board
  onMetadataChanged(metadata: {
    org: string;
    domain: string;
    project: string;
    team: string;
  }): void {
    this.agentMetadata = metadata;
  }

  // Handle agent details changes from canvas board
  onAgentDetailsChanged(details: {
    name: string;
    useCaseDetails: string;
  }): void {
    console.log('Agent details changed:', details);
    this.agentName = details.name;
    this.agentDetail = details.useCaseDetails;
  }

  // Get active tab label for display
  getActiveTabLabel(): string {
    const activeTabObj = this.tabs.find((tab) => tab.value === this.activeTab);
    return activeTabObj ? activeTabObj.label : 'Item';
  }

  // Create new item action (dynamic based on active tab)
  onCreateNewItem(): void {
    const tabLabel = this.getActiveTabLabel();

    // Navigate to appropriate creation page based on active tab
    switch (this.activeTab) {
      case 'prompts':
        this.router.navigate(['/libraries/prompts/create']);
        break;
      case 'models':
        this.router.navigate(['/libraries/models/create']);
        break;
      case 'knowledge':
        this.router.navigate(['/libraries/knowledge-base/create']);
        break;
      case 'tools':
        this.router.navigate(['/libraries/tools/create']);
        break;
      case 'guardrails':
        this.router.navigate(['/libraries/guardrails/create']);
        break;
      default:
        console.warn(`No creation route defined for tab: ${this.activeTab}`);
    }
  }

  // Calculate automatic position based on hierarchy and node type
  private calculateAutoPosition(nodeType: string): { x: number; y: number } {
    if (this.isExecuteMode) {
      // Execute mode - center nodes in the middle of the canvas
      const centerX = 75; // Center position for the canvas (adjust this value to center perfectly)
      const startY = 80; // Start position from top
      const verticalSpacing = 70; // Uniform spacing between nodes

      // Calculate position based on current number of nodes
      const nodeIndex = this.buildAgentNodes.length;
      return {
        x: centerX,
        y: startY + nodeIndex * verticalSpacing,
      };
    }

    // Build mode - Fixed predictable positioning based on hierarchy
    return this.calculateHierarchyBasedPosition(nodeType);
  }

  // Calculate position based on node hierarchy and existing nodes
  private calculateHierarchyBasedPosition(nodeType: string): {
    x: number;
    y: number;
  } {
    const startX = 100; // Fixed starting X position
    const startY = 100; // Fixed starting Y position
    const nodeSpacing = 180; // Space between nodes

    // Define the flow order: prompt → model → knowledge → guardrail → tool
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Get current hierarchy position for this node type
    const hierarchyIndex = flowOrder.indexOf(nodeType);

    // Count existing nodes of each type to determine position within type
    const nodesByType: { [key: string]: number } = {};
    this.buildAgentNodes.forEach((node) => {
      nodesByType[node.type] = (nodesByType[node.type] || 0) + 1;
    });

    // Calculate position based on hierarchy and count within type
    const typeCount = nodesByType[nodeType] || 0;

    // Position calculation: first node at start, second to right, third below second, etc.
    let totalNodesBeforeThisType = 0;
    for (let i = 0; i < hierarchyIndex; i++) {
      totalNodesBeforeThisType += nodesByType[flowOrder[i]] || 0;
    }

    const absoluteIndex = totalNodesBeforeThisType + typeCount;

    // Use the same pattern as before but based on hierarchy position
    if (absoluteIndex === 0) {
      // First node - top left
      return { x: startX, y: startY };
    } else if (absoluteIndex === 1) {
      // Second node - to the right of first
      return { x: startX + nodeSpacing, y: startY };
    } else if (absoluteIndex === 2) {
      // Third node - below second
      return { x: startX + nodeSpacing, y: startY + nodeSpacing };
    } else if (absoluteIndex === 3) {
      // Fourth node - to the right of third
      return { x: startX + nodeSpacing * 2, y: startY + nodeSpacing };
    } else if (absoluteIndex === 4) {
      // Fifth node - below fourth
      return { x: startX + nodeSpacing * 2, y: startY + nodeSpacing * 2 };
    } else {
      // Additional nodes - continue in a grid pattern
      const row = Math.floor(absoluteIndex / 3);
      const col = absoluteIndex % 3;
      return {
        x: startX + col * nodeSpacing,
        y: startY + row * nodeSpacing,
      };
    }
  }

  // Get positions based on the order nodes are added (alternating horizontal/vertical)
  private getFixedNodePositions(): { [key: string]: { x: number; y: number } } {
    if (this.isExecuteMode) {
      // Execute mode - vertical layout centered in the middle of canvas
      const centerX = 10; // Center position for the canvas (adjust this value to center perfectly)
      const startY = 160;
      const verticalSpacing = 70; // Uniform spacing between nodes

      return {
        prompt: { x: centerX, y: startY },
        model: { x: centerX, y: startY + verticalSpacing },
        knowledge: { x: centerX, y: startY + verticalSpacing * 2 },
        tool: { x: centerX, y: startY + verticalSpacing * 3 },
        guardrail: { x: centerX, y: startY + verticalSpacing * 4 }, // For individual agents
        default: { x: centerX, y: startY + verticalSpacing * 5 },
      };
    } else {
      // Build mode - Uniform grid layout with fixed coordinates
      const gridSpacing = 180; // Uniform spacing between nodes
      const startX = 120; // Starting X position
      const startY = 120; // Starting Y position
      const nodesPerRow = 3; // Number of nodes per row

      // Calculate position based on the number of existing nodes
      const nodeCount = this.buildAgentNodes.length;
      const row = Math.floor(nodeCount / nodesPerRow);
      const col = nodeCount % nodesPerRow;

      return {
        default: {
          x: startX + col * gridSpacing,
          y: startY + row * gridSpacing,
        },
      };
    }
  }

  // Create connections based on hierarchy and flow order
  private createAutomaticConnections(newNode: BuildAgentNodeData): void {
    // After adding a new node, recreate all connections to maintain proper hierarchy
    this.createAgentFlowConnections(this.buildAgentNodes);
    console.log('Node added:', newNode.name, 'Type:', newNode.type);
  }

  // Simple method to create a connection between two nodes
  private createConnection(sourceId: string, targetId: string): void {
    // Check if connection already exists
    const existingConnection = this.canvasEdges.find(
      (edge) => edge.source === sourceId && edge.target === targetId,
    );

    if (existingConnection) {
      return; // Connection already exists
    }

    const newEdge: CanvasEdge = {
      id: `edge_${sourceId}_${targetId}`,
      source: sourceId,
      target: targetId,
      animated: false, // Simple static connections
    };

    this.canvasEdges = [...this.canvasEdges, newEdge];
  }

  // Create connections between nodes in the proper agent flow order
  private createAgentFlowConnections(nodes: BuildAgentNodeData[]): void {
    if (nodes.length < 2) return; // Need at least 2 nodes to create connections

    // Clear existing edges first
    this.canvasEdges = [];

    // Define the flow order: prompt → model → knowledge → guardrails → tools
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Group nodes by type
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {};
    nodes.forEach((node) => {
      if (!nodesByType[node.type]) {
        nodesByType[node.type] = [];
      }
      nodesByType[node.type].push(node);
    });

    // Create connections following the flow order with proper hierarchy
    let lastNodeOfPreviousType: BuildAgentNodeData | null = null;

    flowOrder.forEach((nodeType) => {
      const currentNodes = nodesByType[nodeType] || [];

      if (currentNodes.length > 0) {
        // Connect the last node of the previous type to the first node of current type
        if (lastNodeOfPreviousType) {
          this.createConnection(lastNodeOfPreviousType.id, currentNodes[0].id);
        }

        // If there are multiple nodes of the same type, connect them in sequence
        for (let i = 0; i < currentNodes.length - 1; i++) {
          this.createConnection(currentNodes[i].id, currentNodes[i + 1].id);
        }

        // Update the last node to be the last node of current type
        lastNodeOfPreviousType = currentNodes[currentNodes.length - 1];
      }
    });

    console.log(
      'Created agent flow connections:',
      this.canvasEdges.length,
      'connections',
    );
  }

  // Create consolidated execute nodes from build agent nodes
  private createExecuteNodes(): void {
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      tool: [],
      guardrail: [],
    };

    // Group nodes by type
    this.buildAgentNodes.forEach((node) => {
      if (nodesByType[node.type]) {
        nodesByType[node.type].push(node);
      }
    });

    // Create execute nodes for types that have nodes
    this.executeNodes = [];
    const nodeTypes = ['prompt', 'model', 'knowledge', 'tool', 'guardrail'];
    const centerX = 10; // Center position for the canvas
    const startY = 150;
    const verticalSpacing = 70;

    let nodeIndex = 0;
    nodeTypes.forEach((type) => {
      if (nodesByType[type].length > 0) {
        this.executeNodes.push({
          type: type as 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail',
          nodes: nodesByType[type],
          position: {
            x: centerX,
            y: startY + nodeIndex * verticalSpacing,
          },
        });
        nodeIndex++;
      }
    });

    // Update canvas nodes for execute mode
    this.canvasNodes = this.executeNodes.map((executeNode, index) => ({
      id: `execute-${executeNode.type}`,
      type: 'build-agent',
      data: {
        id: `execute-${executeNode.type}`,
        type: executeNode.type,
        name: executeNode.type,
        position: executeNode.position,
        width: 55,
      },
      position: executeNode.position,
    }));

    // Create connections for execute mode (simple vertical flow)
    this.canvasEdges = [];
    for (let i = 0; i < this.executeNodes.length - 1; i++) {
      this.canvasEdges.push({
        id: `execute-edge-${i}`,
        source: `execute-${this.executeNodes[i].type}`,
        target: `execute-${this.executeNodes[i + 1].type}`,
      });
    }
  }

  // Recalculate all node positions (used when switching between normal and execute modes)
  private recalculateAllNodePositions(): void {
    if (this.isExecuteMode) {
      // In execute mode, create consolidated nodes
      this.createExecuteNodes();
      return;
    }

    const nodeWidth = 90; // Build mode node width

    // Force recalculation of all node positions with proper indexing for build mode
    for (let i = 0; i < this.buildAgentNodes.length; i++) {
      // In build mode, use the same predictable positioning pattern
      const startX = 100;
      const startY = 100;
      const nodeSpacing = 180;

      let newPosition: { x: number; y: number };

      if (i === 0) {
        // First node - top left
        newPosition = { x: startX, y: startY };
      } else if (i === 1) {
        // Second node - to the right of first
        newPosition = { x: startX + nodeSpacing, y: startY };
      } else if (i === 2) {
        // Third node - below second
        newPosition = { x: startX + nodeSpacing, y: startY + nodeSpacing };
      } else if (i === 3) {
        // Fourth node - to the right of third
        newPosition = { x: startX + nodeSpacing * 2, y: startY + nodeSpacing };
      } else if (i === 4) {
        // Fifth node - below fourth
        newPosition = {
          x: startX + nodeSpacing * 2,
          y: startY + nodeSpacing * 2,
        };
      } else {
        // Additional nodes - continue in a grid pattern
        const row = Math.floor(i / 3);
        const col = i % 3;
        newPosition = {
          x: startX + col * nodeSpacing,
          y: startY + row * nodeSpacing,
        };
      }

      this.buildAgentNodes[i].position = newPosition;
    }

    // Update canvas nodes to match
    for (let i = 0; i < this.canvasNodes.length; i++) {
      const canvasNode = this.canvasNodes[i];
      const buildAgentNode = this.buildAgentNodes.find(
        (n) => n.id === canvasNode.id,
      );
      if (buildAgentNode) {
        this.canvasNodes[i].position = buildAgentNode.position;
        this.canvasNodes[i].data.position = buildAgentNode.position;
        this.canvasNodes[i].data.width = nodeWidth; // Update node width
      }
    }

    // Force change detection with a slight delay to ensure smooth transition
    setTimeout(() => {
      this.canvasNodes = [...this.canvasNodes];
      this.buildAgentNodes = [...this.buildAgentNodes];
      this.cdr.detectChanges();
    }, 100);
  }

  // Get execute node data for a given canvas node
  getExecuteNodeData(node: CanvasNode): ExecuteNodeData | undefined {
    if (!this.isExecuteMode) return undefined;

    const nodeType = node.id.replace('execute-', '');
    return this.executeNodes.find(
      (executeNode) => executeNode.type === nodeType,
    );
  }

  // Generate a unique node ID - similar to workflow editor
  generateNodeId(): string {
    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  // Load agent data from catalogue
  private loadAgentData(agentId: string): void {
    console.log(
      'Loading agent data for ID:',
      agentId,
      'Type:',
      this.currentAgentType,
    );
    console.log('Current allToolItems state:', this.allToolItems);

    // Ensure labels data is loaded first before loading agent data
    if (!this.labelsCache) {
      console.log('Labels not loaded yet, loading labels first...');
      this.agentService.getLabels().subscribe({
        next: (response: any) => {
          console.log('Labels loaded for agent mapping:', response);
          this.labelsCache = response;

          // Load all data from cached labels
          this.loadModelsFromCache();
          this.loadKnowledgeBaseFromCache();
          this.loadGuardrailsFromCache();

          // Now load the agent data
          this.loadAgentDataAfterLabels(agentId);
        },
        error: (error) => {
          console.error('Error loading labels for agent mapping:', error);
          // Proceed anyway with existing data
          this.loadAgentDataAfterLabels(agentId);
        },
      });
      return;
    }

    this.loadAgentDataAfterLabels(agentId);
  }

  // Load agent data after ensuring labels are loaded
  private loadAgentDataAfterLabels(agentId: string): void {
    if (this.currentAgentType === 'collaborative') {
      this.agentService.getCollaborativeAgentById(agentId).subscribe({
        next: (response) => {
          console.log('Collaborative agent data loaded:', response);
          console.log('allToolItems at mapping time:', this.allToolItems);
          if (response && (response.agentDetails || response.agentDetail)) {
            this.mapCollaborativeAgentDataToCanvas(response);
          } else {
            console.warn(
              'No collaborative agent data found in response:',
              response,
            );
            this.showErrorMessage(
              'Agent Not Found',
              'No agent data found. The agent might not exist or you might not have permission to view it.',
            );
          }
        },
        error: (error) => {
          console.error('Error loading collaborative agent data:', error);
          this.showErrorMessage(
            'Load Failed',
            'Failed to load collaborative agent data. Please check the console for details.',
          );
        },
      });
    } else {
      this.agentService.getAgentById(agentId).subscribe({
        next: (response) => {
          console.log('Individual agent data loaded:', response);
          console.log('allToolItems at mapping time:', this.allToolItems);
          if (response && (response.useCaseName || response.name)) {
            this.mapAgentDataToCanvas(response);
          } else {
            console.warn(
              'No individual agent data found in response:',
              response,
            );
            this.showErrorMessage(
              'Agent Not Found',
              'No agent data found. The agent might not exist or you might not have permission to view it.',
            );
          }
        },
        error: (error) => {
          console.error('Error loading individual agent data:', error);
          this.showErrorMessage(
            'Load Failed',
            'Failed to load individual agent data. Please check the console for details.',
          );
        },
      });
    }
  }

  // Map agent data to canvas board
  private mapAgentDataToCanvas(agentData: any): void {
    console.log('Raw individual agent response:', agentData);

    if (!agentData) {
      console.error('No individual agent data provided');
      return;
    }

    // Set basic agent information using the actual API response structure
    // In duplicate mode, clear the name field so user can enter a new name
    if (this.isDuplicateMode) {
      this.agentName = ''; // Clear name for duplicate mode
    } else {
      if (agentData.useCaseName) {
        this.agentName = agentData.useCaseName;
      } else if (agentData.name) {
        this.agentName = agentData.name;
      }
    }

    // Extract agent code for API calls
    if (agentData.useCaseCode) {
      this.agentCode = agentData.useCaseCode;
    } else if (agentData.code) {
      this.agentCode = agentData.code;
    } else {
      // Fallback: generate code from agent name
      this.agentCode = this.agentName.trim().replace(/\s+/g, '_').toUpperCase();
    }

    if (agentData.useCaseDetails) {
      this.agentDetail = agentData.useCaseDetails;
    } else if (agentData.description) {
      this.agentDetail = agentData.description;
    } else if (agentData.agentDetail) {
      this.agentDetail = agentData.agentDetail;
    }

    // Set metadata from the API response
    if (agentData.organizationPath) {
      const pathParts = agentData.organizationPath.split('@');
      console.log(
        'Parsing organizationPath:',
        agentData.organizationPath,
        'parts:',
        pathParts,
      );
      if (pathParts.length >= 5) {
        this.agentMetadata = {
          org: pathParts[1] || agentData.org || '',
          domain: pathParts[2] || agentData.domain || '',
          project: pathParts[3] || agentData.project || '',
          team: pathParts[4] || agentData.team || '',
        };
      }
    } else {
      this.agentMetadata = {
        org: agentData.org || '',
        domain: agentData.domain || '',
        project: agentData.project || '',
        team: agentData.team || '',
      };
    }

    console.log('Agent metadata set:', this.agentMetadata);

    // Map individual agent data to canvas nodes based on config array
    // Use a temporary array to collect nodes in hierarchy order
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      guardrail: [],
      tool: [],
    };
    let nodeCounter = 1;

    // Add prompt node first (always add prompt for individual agents)
    console.log('=== PROMPT MAPPING DEBUG ===');
    console.log('Agent data prompt:', agentData.prompt);
    console.log('Available prompts:', this.allToolItems['prompts']);
    console.log('Prompts count:', this.allToolItems['prompts']?.length);

    if (agentData.prompt || agentData.useCaseDetails || agentData.useCaseName) {
      let promptName = agentData.prompt || 'Default Prompt';

      // Try to find the exact prompt by name from the loaded prompts
      let promptData = this.allToolItems['prompts']?.find(
        (p) =>
          p.name === promptName ||
          p.name.toLowerCase() === promptName.toLowerCase(),
      );

      // If not found by name, use the first available prompt as fallback
      if (!promptData && this.allToolItems['prompts']?.length > 0) {
        promptData = this.allToolItems['prompts'][0];
        promptName = promptData.name; // Use the actual prompt name from the data
        console.log('Using fallback prompt:', promptName);
      }

      // Always create a prompt node (essential for individual agents)
      const promptNode: BuildAgentNodeData = {
        id: `prompt-${nodeCounter++}`,
        name: promptName,
        type: 'prompt',
        position: { x: 0, y: 0 }, // Will be calculated later
      };
      nodesByType['prompt'].push(promptNode);
      console.log('Added prompt node:', promptNode);
    } else {
      console.log('No prompt data found in agent response');
    }

    // Process the config array to extract model, tools, knowledge bases, etc.
    if (agentData.config && Array.isArray(agentData.config)) {
      agentData.config.forEach((category: any) => {
        if (category.config && Array.isArray(category.config)) {
          category.config.forEach((configItem: any) => {
            // Store configId for update operations
            if (configItem.configId) {
              const configKey = `${category.categoryId}-${configItem.configKey}`;
              this.agentConfigIds.set(configKey, configItem.configId);
              console.log(
                'Stored configId:',
                configKey,
                '=',
                configItem.configId,
              );
            }
            // Handle MODEL configuration
            if (configItem.configKey === 'MODEL' && configItem.configValue) {
              const modelId = configItem.configValue;
              console.log('Looking for model with ID:', modelId);
              console.log('Available models:', this.allToolItems['models']);

              const modelData = this.allToolItems['models']?.find(
                (m) =>
                  m.id === modelId ||
                  m.id === modelId.toString() ||
                  m.id.toString() === modelId,
              );

              if (modelData) {
                nodesByType['model'].push({
                  id: `model-${nodeCounter++}`,
                  type: 'model',
                  name: modelData.name,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                  originalToolData: modelData, // Store the original tool data for ID retrieval
                });
                console.log('Added model node:', modelData.name);
              } else {
                // Create a placeholder model node if not found
                nodesByType['model'].push({
                  id: `model-${nodeCounter++}`,
                  type: 'model',
                  name: `Model ID: ${modelId}`,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                  originalToolData: {
                    id: modelId,
                    name: `Model ID: ${modelId}`,
                  }, // Store the ID for retrieval
                });
                console.log('Created placeholder model node for ID:', modelId);
              }
            }

            // Handle RAG_KNOWLEDGEBASE_NAME configuration (can contain comma-separated IDs)
            if (
              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&
              configItem.configValue
            ) {
              const kbValue = configItem.configValue.toString(); // Ensure it's a string
              const kbIds = kbValue
                .split(',')
                .map((id: string) => id.trim())
                .filter((id: string) => id); // Split by comma and clean

              console.log('=== KNOWLEDGE BASE MAPPING DEBUG ===');
              console.log('Raw knowledge base value:', kbValue);
              console.log('Parsed knowledge base IDs:', kbIds);
              console.log(
                'Available knowledge bases:',
                this.allToolItems['knowledge'],
              );
              console.log(
                'Knowledge base count:',
                this.allToolItems['knowledge']?.length,
              );

              // Process each knowledge base ID separately to create individual nodes
              kbIds.forEach((kbId: string) => {
                const knowledgeData = this.allToolItems['knowledge']?.find(
                  (k) => {
                    console.log(
                      'Checking KB:',
                      k.name,
                      'ID:',
                      k.id,
                      'Type:',
                      typeof k.id,
                    );
                    return (
                      k.id === kbId ||
                      k.id === kbId.toString() ||
                      k.id.toString() === kbId ||
                      k.name === kbId ||
                      k.name.toLowerCase() === kbId.toLowerCase()
                    );
                  },
                );

                if (knowledgeData) {
                  nodesByType['knowledge'].push({
                    id: `knowledge-${nodeCounter++}`,
                    type: 'knowledge',
                    name: knowledgeData.name,
                    icon: undefined, // Let node component use Lucide icon
                    position: { x: 0, y: 0 }, // Will be calculated later
                    originalToolData: knowledgeData, // Store the original tool data for ID retrieval
                  });
                  console.log('Added knowledge base node:', knowledgeData.name);
                } else {
                  // Create a placeholder knowledge node if not found in labels
                  // Try to get the name from labels API directly
                  let kbName = `Knowledge Base ID: ${kbId}`;
                  if (this.labelsCache && this.labelsCache.categoryLabels) {
                    const iclCategory = this.labelsCache.categoryLabels.find(
                      (cat: any) => cat.categoryId === 2,
                    );
                    if (iclCategory) {
                      const kbLabel = iclCategory.labels.find(
                        (label: any) =>
                          label.labelCode === 'RAG_KNOWLEDGEBASE_NAME',
                      );
                      if (kbLabel && kbLabel.labelValues) {
                        const kbOptions = this.agentService.parseLabelValues(
                          kbLabel.labelValues,
                        );
                        const kbOption = kbOptions.find(
                          (opt) => opt.value === kbId,
                        );
                        if (kbOption) {
                          kbName = kbOption.name;
                        }
                      }
                    }
                  }

                  nodesByType['knowledge'].push({
                    id: `knowledge-${nodeCounter++}`,
                    type: 'knowledge',
                    name: kbName,
                    icon: undefined, // Let node component use Lucide icon
                    position: { x: 0, y: 0 }, // Will be calculated later
                    originalToolData: { id: kbId, name: kbName }, // Store the ID for retrieval
                  });
                  console.log('Created knowledge base node with name:', kbName);
                }
              });
            }

            // Handle GUARDRAIL configurations (only show enabled guardrails)
            if (
              configItem.configKey &&
              configItem.configKey.startsWith('GUARDRAIL_') &&
              (configItem.configValue === 'true' ||
                configItem.configValue === true)
            ) {
              console.log(
                'Looking for guardrail with code:',
                configItem.configKey,
              );
              console.log(
                'Available guardrails:',
                this.allToolItems['guardrails'],
              );

              const guardrailData = this.allToolItems['guardrails']?.find(
                (g) => (g as any).code === configItem.configKey,
              );

              if (guardrailData) {
                nodesByType['guardrail'].push({
                  id: `guardrail-${nodeCounter++}`,
                  type: 'guardrail',
                  name: guardrailData.name,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                });
                console.log('Added guardrail node:', guardrailData.name);
              } else {
                // Create placeholder guardrail node
                const guardrailName = configItem.configKey
                  .replace('GUARDRAIL_', '')
                  .replace(/_/g, ' ');
                nodesByType['guardrail'].push({
                  id: `guardrail-${nodeCounter++}`,
                  type: 'guardrail',
                  name: guardrailName,
                  icon: undefined, // Let node component use Lucide icon
                  position: { x: 0, y: 0 }, // Will be calculated later
                });
                console.log(
                  'Created placeholder guardrail node:',
                  guardrailName,
                );
              }
            }
          });
        }
      });
    }

    // Assemble nodes in hierarchy order and calculate positions
    const nodes: BuildAgentNodeData[] = [];
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Temporarily set buildAgentNodes to empty array for position calculation
    this.buildAgentNodes = [];

    flowOrder.forEach((nodeType) => {
      nodesByType[nodeType].forEach((node) => {
        // Calculate position based on current hierarchy
        node.position = this.calculateHierarchyBasedPosition(nodeType);
        nodes.push(node);
        // Add to buildAgentNodes for next position calculation
        this.buildAgentNodes.push(node);
      });
    });

    // Update build agent nodes with final array
    this.buildAgentNodes = nodes;

    // Update canvas nodes for display
    this.canvasNodes = nodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: this.isExecuteMode ? 55 : 120 },
      position: node.position,
    }));

    // Clear existing edges when loading new agent data
    this.canvasEdges = [];

    // Create connections between nodes in the proper flow order
    this.createAgentFlowConnections(nodes);

    // Update canvas board with the loaded data
    this.cdr.detectChanges();

    // Log the loaded data for debugging
    console.log('Individual agent data mapped to canvas:', {
      agentName: this.agentName,
      agentDetail: this.agentDetail,
      agentMetadata: this.agentMetadata,
      buildAgentNodes: this.buildAgentNodes,
      originalData: agentData,
    });

    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation
    if (this.isDuplicateMode) {
      this.currentAgentId = null;
      console.log('Cleared currentAgentId for duplicate mode');
    }

    // If in execute mode, automatically initialize playground
    if (this.isExecuteMode) {
      console.log('Execute mode detected - initializing playground');
      // Auto-select this agent in the dropdown when execute mode loads
      this.autoSelectCurrentAgentInDropdown();
      setTimeout(() => {
        this.initializeExecuteMode();
      }, 500);
    }
  }

  // Map collaborative agent data to canvas board
  private mapCollaborativeAgentDataToCanvas(response: any): void {
    console.log('Raw collaborative agent response:', response);

    // Handle different response structures
    let agentData;
    if (
      response.agentDetails &&
      Array.isArray(response.agentDetails) &&
      response.agentDetails.length > 0
    ) {
      agentData = response.agentDetails[0];
    } else if (response.agentDetail) {
      agentData = response.agentDetail;
    } else if (response.data) {
      agentData = response.data;
    } else {
      agentData = response;
    }

    if (!agentData) {
      console.error('No agent data found in response');
      return;
    }

    console.log('Mapping collaborative agent data:', agentData);

    // Set basic agent information
    // In duplicate mode, clear the name field so user can enter a new name
    if (this.isDuplicateMode) {
      this.agentName = ''; // Clear name for duplicate mode
    } else {
      if (agentData.name) {
        this.agentName = agentData.name;
      }
    }

    // Extract agent code for API calls
    if (agentData.code) {
      this.agentCode = agentData.code;
    } else if (agentData.useCaseCode) {
      this.agentCode = agentData.useCaseCode;
    } else {
      // Fallback: generate code from agent name
      this.agentCode = this.agentName.trim().replace(/\s+/g, '_').toUpperCase();
    }

    if (agentData.agentDetail) {
      this.agentDetail = agentData.agentDetail;
    }

    // Set metadata from the API response
    this.agentMetadata = {
      org: agentData.org || '',
      domain: agentData.domain || '',
      project: agentData.project || '',
      team: agentData.team || '',
    };

    // Map collaborative agent data to canvas nodes
    // Use a temporary array to collect nodes in hierarchy order
    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {
      prompt: [],
      model: [],
      knowledge: [],
      guardrail: [],
      tool: [],
    };
    let nodeCounter = 1;

    // Add prompt node first (check for prompt field in collaborative agent data)
    if (agentData.prompt) {
      // Try to find the exact prompt by name from the API response
      let promptData = this.allToolItems['prompts']?.find(
        (p) =>
          p.name === agentData.prompt ||
          p.name.toLowerCase() === agentData.prompt.toLowerCase(),
      );

      // If not found by name, use zero shot prompt as fallback for collaborative agents
      if (!promptData) {
        promptData =
          this.allToolItems['prompts']?.find(
            (p) => (p as any).promptType === 'zero shot',
          ) || this.allToolItems['prompts']?.[0];
      }

      if (promptData) {
        const promptNode: BuildAgentNodeData = {
          id: `prompt-${nodeCounter++}`,
          name: agentData.prompt, // Use the actual prompt name from API
          type: 'prompt',
          icon: promptData.icon || this.getIconForType('prompt'),
          position: { x: 0, y: 0 }, // Will be calculated later
        };
        nodesByType['prompt'].push(promptNode);
        console.log('Added collaborative prompt node from API:', promptNode);
      }
    } else if (agentData.agentDetail || agentData.name) {
      // Fallback: Find a matching prompt from the loaded prompts (collaborative agents use "zero shot" prompts)
      const promptData =
        this.allToolItems['prompts']?.find(
          (p) => (p as any).promptType === 'zero shot',
        ) || this.allToolItems['prompts']?.[0];
      if (promptData) {
        const promptNode: BuildAgentNodeData = {
          id: `prompt-${nodeCounter++}`,
          name: promptData.name,
          type: 'prompt',
          icon: promptData.icon || this.getIconForType('prompt'),
          position: { x: 0, y: 0 }, // Will be calculated later
        };
        nodesByType['prompt'].push(promptNode);
        console.log('Added fallback collaborative prompt node:', promptNode);
      }
    }

    // Add model node
    if (agentData.modelDetails) {
      const modelId = agentData.modelDetails.modelId;
      console.log('Looking for collaborative model with ID:', modelId);
      console.log('Available models:', this.allToolItems['models']);

      const modelData = this.allToolItems['models']?.find(
        (m) =>
          m.id === modelId ||
          m.id === modelId.toString() ||
          m.id.toString() === modelId,
      );

      if (modelData) {
        nodesByType['model'].push({
          id: `model-${nodeCounter++}`,
          type: 'model',
          name: modelData.name,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
        });
        console.log('Added collaborative model node:', modelData.name);
      } else {
        // Create a placeholder model node with the model name from API
        nodesByType['model'].push({
          id: `model-${nodeCounter++}`,
          type: 'model',
          name: agentData.modelDetails.model || `Model ID: ${modelId}`,
          icon: undefined, // Let node component use Lucide icon
          position: { x: 0, y: 0 }, // Will be calculated later
        });
        console.log(
          'Created placeholder collaborative model node:',
          agentData.modelDetails.model,
        );
      }
    }

    // Add knowledge base nodes
    if (
      agentData.indexCollectionName &&
      Array.isArray(agentData.indexCollectionName)
    ) {
      agentData.indexCollectionName.forEach((collectionName: string) => {
        console.log('Looking for knowledge base:', collectionName);
        console.log(
          'Available knowledge bases:',
          this.allToolItems['knowledge'],
        );

        const knowledgeData = this.allToolItems['knowledge']?.find(
          (k) =>
            k.name === collectionName ||
            k.name.toLowerCase() === collectionName.toLowerCase(),
        );

        if (knowledgeData) {
          nodesByType['knowledge'].push({
            id: `knowledge-${nodeCounter++}`,
            type: 'knowledge',
            name: knowledgeData.name,
            icon: undefined, // Let node component use Lucide icon
            position: { x: 0, y: 0 }, // Will be calculated later
          });
          console.log('Added knowledge base node:', knowledgeData.name);
        } else {
          // Create a placeholder knowledge node
          nodesByType['knowledge'].push({
            id: `knowledge-${nodeCounter++}`,
            type: 'knowledge',
            name: collectionName,
            icon: undefined, // Let node component use Lucide icon
            position: { x: 0, y: 0 }, // Will be calculated later
          });
          console.log(
            'Created placeholder knowledge base node:',
            collectionName,
          );
        }
      });
    }

    // Add tool nodes (built-in tools)
    if (agentData.tools && Array.isArray(agentData.tools)) {
      agentData.tools.forEach((tool: any) => {
        const toolId = tool.toolId;
        console.log('Looking for built-in tool with ID:', toolId);
        console.log('Available tools:', this.allToolItems['tools']);

        const toolData = this.allToolItems['tools']?.find(
          (t) =>
            t.id === toolId ||
            t.id === toolId.toString() ||
            t.id.toString() === toolId,
        );

        if (toolData) {
          nodesByType['tool'].push({
            id: `tool-${nodeCounter++}`,
            type: 'tool',
            name: toolData.name,
            icon: undefined, // Let node component use Lucide icon
            position: { x: 0, y: 0 }, // Will be calculated later
          });
          console.log('Added built-in tool node:', toolData.name);
        } else {
          // Create a placeholder tool node with the tool name from API
          nodesByType['tool'].push({
            id: `tool-${nodeCounter++}`,
            type: 'tool',
            name: tool.toolName || `Tool ID: ${toolId}`,
            icon: undefined, // Let node component use Lucide icon
            position: { x: 0, y: 0 }, // Will be calculated later
          });
          console.log('Created placeholder built-in tool node:', tool.toolName);
        }
      });
    }

    // Add user tool nodes (user-defined tools)
    if (agentData.userTools && Array.isArray(agentData.userTools)) {
      agentData.userTools.forEach((userTool: any) => {
        const userToolId = userTool.toolId;
        console.log('Looking for user tool with ID:', userToolId);
        console.log('Available user tools:', this.allToolItems['tools']);

        // Look for user tools (they have 'user-' prefix in our system)
        const userToolData = this.allToolItems['tools']?.find(
          (t) =>
            t.id === `user-${userToolId}` ||
            t.id === userToolId ||
            t.id === userToolId.toString() ||
            t.id.toString() === userToolId,
        );

        if (userToolData) {
          nodesByType['tool'].push({
            id: `tool-${nodeCounter++}`,
            type: 'tool',
            name: userToolData.name,
            icon: undefined, // Let node component use Lucide icon
            position: { x: 0, y: 0 }, // Will be calculated later
          });
          console.log('Added user tool node:', userToolData.name);
        } else {
          // Create a placeholder user tool node
          nodesByType['tool'].push({
            id: `tool-${nodeCounter++}`,
            type: 'tool',
            name: userTool.toolName || `User Tool ID: ${userToolId}`,
            icon: undefined, // Let node component use Lucide icon
            position: { x: 0, y: 0 }, // Will be calculated later
          });
          console.log('Created placeholder user tool node:', userTool.toolName);
        }
      });
    }

    // Add a default prompt node if none exists (collaborative agents should have prompts)
    if (nodesByType['prompt'].length === 0) {
      const defaultPrompt = this.allToolItems['prompts']?.find((p) => {
        const promptType = (p as any).type?.trim(); // Cast to any to access the prompt type property
        return promptType === 'zero shot';
      });
      if (defaultPrompt) {
        nodesByType['prompt'].push({
          id: `prompt-${nodeCounter++}`,
          type: 'prompt',
          name: defaultPrompt.name,
          icon: defaultPrompt.icon || this.getIconForType('prompt'),
          position: { x: 0, y: 0 }, // Will be calculated later
        });
      }
    }

    // Assemble nodes in hierarchy order and calculate positions
    const nodes: BuildAgentNodeData[] = [];
    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];

    // Temporarily set buildAgentNodes to empty array for position calculation
    this.buildAgentNodes = [];

    flowOrder.forEach((nodeType) => {
      nodesByType[nodeType].forEach((node) => {
        // Calculate position based on current hierarchy
        node.position = this.calculateHierarchyBasedPosition(nodeType);
        nodes.push(node);
        // Add to buildAgentNodes for next position calculation
        this.buildAgentNodes.push(node);
      });
    });

    // Update build agent nodes with final array
    this.buildAgentNodes = nodes;

    // Update canvas nodes for display
    this.canvasNodes = nodes.map((node) => ({
      id: node.id,
      type: 'build-agent',
      data: { ...node, width: this.isExecuteMode ? 55 : 120 },
      position: node.position,
    }));

    // Clear existing edges when loading new agent data
    this.canvasEdges = [];

    // Create connections between nodes in the proper flow order
    this.createAgentFlowConnections(nodes);

    // Update canvas board with the loaded data
    this.cdr.detectChanges();

    // Log the loaded data for debugging
    console.log('Collaborative agent data mapped to canvas:', {
      agentName: this.agentName,
      agentDetail: this.agentDetail,
      agentMetadata: this.agentMetadata,
      buildAgentNodes: this.buildAgentNodes,
      originalData: agentData,
    });

    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation
    if (this.isDuplicateMode) {
      this.currentAgentId = null;
      console.log('Cleared currentAgentId for duplicate mode');
    }

    // If in execute mode, automatically initialize playground
    if (this.isExecuteMode) {
      console.log('Execute mode detected - initializing playground');
      setTimeout(() => {
        this.initializeExecuteMode();
      }, 500);
    }
  }

  // Simple method to auto-select current agent in dropdown
  private autoSelectCurrentAgentInDropdown(): void {
    if (!this.agentName) {
      console.log('No agent name available for auto-selection');
      return;
    }

    // Load agents and then auto-select
    this.loadAgentNamesForPromptOptions();

    // Try to auto-select after a short delay
    setTimeout(() => {
      if (this.promptOptions && this.promptOptions.length > 0) {
        // Find the current agent in the dropdown options
        const currentAgentOption = this.promptOptions.find(
          (option) =>
            option.name === this.agentName ||
            option.name.toLowerCase() === this.agentName.toLowerCase(),
        );

        if (currentAgentOption) {
          this.selectedPrompt = currentAgentOption.name; // Use name for dropdown display
          this.agentCode = currentAgentOption.value.toString(); // Store agent code
          this.onPromptChanged(currentAgentOption);
          this.cdr.detectChanges();
        }
      }
    }, 1500); // Give more time for the dropdown to load
  }

  // Initialize execute mode - automatically open playground with agent selected
  private initializeExecuteMode(): void {
    console.log(
      'Initializing execute mode for agent:',
      this.agentName,
      'with code:',
      this.agentCode,
    );

    // Ensure agent code is set for API calls
    if (!this.agentCode && this.agentName) {
      this.agentCode = this.agentName.trim().replace(/\s+/g, '_').toUpperCase();
      console.log('Generated agent code from name:', this.agentCode);
    }

    // Recalculate node positions for execute mode (vertical layout)
    this.recalculateAllNodePositions();

    // Initialize the chat interface with personalized greeting
    this.chatMessages = [
      {
        from: 'ai',
        text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`,
      },
    ];

    // Force UI update
    this.cdr.detectChanges();

    console.log(
      'Execute mode initialized successfully with agent code:',
      this.agentCode,
    );
  }

  // Method to handle agent selection from card click
  public selectAgentFromCard(agentData: any): void {
    console.log('Agent selected from card:', agentData);

    // Store the selected agent data
    this.autoSelectedAgentFromCard = agentData;

    // Set agent properties
    this.agentName = agentData.name;
    this.agentCode = agentData.code;
    this.selectedAgentMode = agentData.code;
    this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;

    // Auto-select in dropdown (keep dropdown enabled)
    this.selectedPrompt = agentData.name;

    // Update the prompt options to include the selected agent if not already present
    if (this.promptOptions && this.promptOptions.length > 0) {
      const existingOption = this.promptOptions.find(
        (option) =>
          option.value === agentData.code || option.name === agentData.name,
      );

      if (!existingOption) {
        // Add the selected agent to the options
        this.promptOptions.push({
          value: agentData.code,
          name: agentData.name,
          agentData: agentData,
        } as any);
      }
    }

    console.log('Agent auto-selected in dropdown:', {
      name: this.agentName,
      code: this.agentCode,
      mode: this.selectedAgentMode,
      useCaseIdentifier: this.selectedUseCaseIdentifier,
    });

    // Force change detection
    this.cdr.detectChanges();
  }

  // Navigation
  goBack(): void {
    this.router.navigate(['/agents']);
  }

  // Test method to check the individual agent API - you can call this from browser console
  public testIndividualAgentAPI(): void {
    console.log('Testing individual agent API...');
    this.agentService.getIndividualAgentMinDetails().subscribe({
      next: (response) => {
        console.log('✅ API Test Success - Raw response:', response);
        console.log('✅ Response type:', typeof response);
        console.log('✅ Is array:', Array.isArray(response));
        if (Array.isArray(response)) {
          console.log('✅ Array length:', response.length);
          console.log('✅ First item:', response[0]);
        }
      },
      error: (error) => {
        console.error('❌ API Test Failed:', error);
        console.error('❌ Error status:', error.status);
        console.error('❌ Error message:', error.message);
      },
    });
  }

  // Popup handling methods
  showSuccessMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.showSuccessPopup = true;
  }

  showErrorMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.showErrorPopup = true;
  }

  showWarningMessage(title: string, message: string): void {
    this.popupTitle = title;
    this.popupMessage = message;
    this.showWarningPopup = true;
  }

  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  // Handle success popup confirmation
  onSuccessConfirm(): void {
    this.closeSuccessPopup();
    // Playground is already opened automatically after save
  }

  closeErrorPopup(): void {
    this.showErrorPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  closeWarningPopup(): void {
    this.showWarningPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
  }

  // Handle warning popup confirmation - proceed with save
  onWarningConfirm(): void {
    this.closeWarningPopup();

    // Proceed with save based on agent type
    if (this.currentAgentType === 'individual') {
      this.performIndividualAgentSave();
    } else if (this.currentAgentType === 'collaborative') {
      this.performCollaborativeAgentSave();
    }
  }

  // Handle warning popup cancel - don't save
  onWarningCancel(): void {
    this.closeWarningPopup();
    // User chose not to save, do nothing
  }
}
