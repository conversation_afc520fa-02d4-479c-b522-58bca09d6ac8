@import "./assets/styles/themes";

@import "./assets/styles/variables";

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* These variables are now defined in _variables.scss */

/* Theme transition optimization */
.theme-transition * {
  transition-property:
    background-color, background-image, color, border-color, box-shadow, opacity;
  transition-duration: 0.3s !important;
  transition-timing-function: ease !important;
}

body,
html {
  height: 100%;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: var(--font-family);
  font-weight: var(--font-weight-regular);
  // background: linear-gradient(180deg, #eef8ff, #d9e8ff);
  background: #fafafa;
}

/* Define the glassmorphism effect classes */
@supports (backdrop-filter: blur(10px)) {
  .glass-effect {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

@supports not (backdrop-filter: blur(10px)) {
  .glass-effect {
    background: var(--glass-fallback-bg, rgba(255, 255, 255, 0.65)) !important;
  }
}

/* Router outlet wrapper */
router-outlet {
  display: none;
}

/* Global container class */
.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 16px;
  }
}

/* Content scrollable area */
.scrollable-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 24px 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover);
  }
}

/* Ensures Angular component host elements expand properly */
app-root,
app-login,
app-dashboard,
app-libraries,
app-knowledge-base,
app-create-knowledge-base {
  display: block;
  height: 100%;
  width: 100%;
}

/* Global form styles */
input,
select,
textarea,
button {
  font-family: var(--font-family);
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.p-0 {
  padding: 0;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-5 {
  margin-bottom: 2rem;
}

.mt-5 {
  margin-top: 2rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.p-4 {
  padding: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* Dark theme specific adjustments - using variables from _variables.scss */
.dark-theme {
  .glass-effect {
    background: var(--glass-bg);
    border-color: var(--glass-border);
  }

  .scrollable-content {
    &::-webkit-scrollbar-thumb {
      background-color: var(--scrollbar-thumb);
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: var(--scrollbar-thumb-hover);
    }
  }
}

.cards-container {
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.cards-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}
